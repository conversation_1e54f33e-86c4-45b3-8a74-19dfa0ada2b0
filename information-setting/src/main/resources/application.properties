logging.config=classpath:log4j2.xml

spring.application.name=information-setting-service
spring.profiles.active=test
server.port=25016

eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
eureka.client.serviceUrl.defaultZone=http://${eureka.instance.hostname}/eureka/1
spring.messages.basename=errormessage

spring.jpa.properties.hibernate.show_sql=true

spring.datasource.primary.initialSize=1
spring.datasource.primary.maxActive=20
spring.datasource.primary.maxAge=120000
spring.datasource.primary.validationQuery=select 1
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.primary.time-between-eviction-runs-millis=10000
spring.datasource.primary.min-evictable-idle-time-millis=20000
spring.datasource.primary.test-while-idle=true
spring.datasource.primary.test-on-borrow=false

#routingkey
mq.cms.rich.text.view.count=cms:pages-server:cms:rich:text:view:count

mt.api.zxlist.url=${mt.api.baseurl}/MIS-Adapter/gxwAdapter
mt.api.zxpage.url=${mt.api.baseurl}/MIS-Adapter/articleAction

aliyun.endpoint=sts.aliyuncs.com
aliyun.accessKeyId=LTAIbj3N6N1c1a3d
aliyun.accessKeySecret=J8YGX0Xc6Syi7HRy5uwIfIMAYtUAou
aliyun.roleArn=acs:ram::********:role/aliyunosstokengeneratorrole
aliyun.roleSessionName=session-name

session.time.out=60
default.information.node=00101
