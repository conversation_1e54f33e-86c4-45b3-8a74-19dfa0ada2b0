logging.level.com.apec=DEBUG
workerId=1

eureka.client.serviceUrl.defaultZone=http://************:1111/eureka/

spring.datasource.primary.url=********************************************************************************************************************************************
spring.datasource.primary.username=root
spring.datasource.primary.password=123456

# mongodb setting
spring.data.mongodb.uri=mongodb://*************:27017/information-dev

redis.database=0
redis.host=************
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

ftp.host=************
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/

spring.redis.host=************
spring.redis.port=7000
spring.redis.host2=************
spring.redis.port2=7001
spring.redis.timeout=5000
spring.redis.maxRedirections=5

spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

magpie.market.list=http://************:30005/BASE-MARKET/facademarket/findListExt.magpie
