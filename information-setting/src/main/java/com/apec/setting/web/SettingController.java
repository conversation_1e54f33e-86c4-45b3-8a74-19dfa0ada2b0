package com.apec.setting.web;

import com.alibaba.fastjson.JSONArray;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.HttpRequestUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.base.MyController;
import com.apec.information.constant.AliyunParameter;
import com.apec.information.constant.InformationConstants;
import com.apec.information.dto.LabelDTO;
import com.apec.information.dto.SettingCategoryDTO;
import com.apec.information.dto.SettingInfoDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.util.AliyunTokenUtil;
import com.apec.information.vo.LabelVO;
import com.apec.information.vo.MarketVO;
import com.apec.information.vo.SettingCategoryVO;
import com.apec.information.vo.SettingInfoVO;
import com.apec.setting.service.LabelService;
import com.apec.setting.service.SettingCategoryService;
import com.apec.setting.service.SettingService;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 */
@RestController
@RefreshScope
@RequestMapping(value = "/setting")
public class SettingController extends MyController
{
    private static Logger logger = LoggerFactory.getLogger(SettingController.class);

    @Autowired
    private AliyunParameter aliyunParameter;

    @Autowired
    private SettingService settingService;

    @Autowired
    private SettingCategoryService categoryService;

    @Autowired
    private LabelService labelService;

    /**
     * 获取阿里云的token
     * @return
     */
    @RequestMapping(value = "/getUploadFileToken", method = RequestMethod.POST)
    public String getUploadFileToken()
    {
        logger.info("Get aliyun token");
        AssumeRoleResponse.Credentials response = AliyunTokenUtil
            .getToken(aliyunParameter.getAccessKeyId(), aliyunParameter.getAccessKeySecret(),
                      aliyunParameter.getEndpoint(), aliyunParameter.getRoleArn(),
                      aliyunParameter.getRoleSessionName());
        Map<String, String> map = new HashMap<>(5);
        map.put("accessKeyId", response.getAccessKeyId());
        map.put("accessKeySecret", response.getAccessKeySecret());
        map.put("securityToken", response.getSecurityToken());
        return success(map);
    }

    @Value("${magpie.market.list}")
    private String magpieMarketList;

    /**
     * 获取阿里云的token
     * @return
     */
    @RequestMapping(value = "/marketList", method = RequestMethod.POST)
    public String marketList() throws Exception
    {
        logger.info("Get market list");
        String jsonStr = HttpRequestUtils.post(magpieMarketList, Maps.newHashMap(), "");
        ResultData<JSONArray> resultData = JsonUtils.parseObject(jsonStr, ResultData.class);
        JSONArray data = resultData.getData();
        List<MarketVO> result = data.toJavaList(MarketVO.class);
        return success(result);
    }

    /**
     * 获取标签列表
     * @return
     */
    @RequestMapping(value = "/listLabel", method = RequestMethod.POST)
    public String labelList(@RequestBody String jsonStr)
    {
        logger.info("Get label list: {}", jsonStr);
        LabelDTO labelDTO = getFormJSON(jsonStr, LabelDTO.class);
        List<LabelVO> list = labelService.list(labelDTO.getOrgNo());
        return success(list);
    }

    /**
     * 新增标签
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insertLabel", method = RequestMethod.POST)
    public String insertLabel(@RequestBody String jsonStr)
    {
        logger.info("insert label list: {}", jsonStr);
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        LabelDTO labelDTO = getFormJSON(jsonStr, LabelDTO.class);
        labelService.insert(labelDTO, modify);
        return success(INSERT_SUCCESS);
    }

    /**
     * 新增标签
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/pickLabel", method = RequestMethod.POST)
    public String pickLabel(@RequestBody String jsonStr)
    {
        logger.info("pick label list: {}", jsonStr);
        LabelDTO labelDTO = getFormJSON(jsonStr, LabelDTO.class);
        labelService.insert(labelDTO, labelDTO.getOrgNo());
        return success(INSERT_SUCCESS);
    }

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/listCategory", method = RequestMethod.POST)
    public String listCategory(@RequestBody String jsonStr)
    {
        logger.info("List category：{}", jsonStr);
        List<SettingCategoryVO> result = categoryService.list();
        return success(result);
    }

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/pageCategory", method = RequestMethod.POST)
    public String pageCategory(@RequestBody String jsonStr)
    {
        logger.info("Page category：{}", jsonStr);
        SettingCategoryDTO categoryDTO = getFormJSON(jsonStr, SettingCategoryDTO.class);
        PageRequest pageRequest = genPageRequest(categoryDTO);
        PageDTO<SettingCategoryVO> result = categoryService.page(categoryDTO, pageRequest);
        return success(result);
    }

    /**
     * 根据 类目ID 进行删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/deleteCategory", method = RequestMethod.POST)
    public String deleteCategory(@RequestBody String jsonStr)
    {
        logger.info("Delete category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            SettingCategoryDTO categoryDTO = getFormJSON(jsonStr, SettingCategoryDTO.class);
            Assert.hasLength(categoryDTO.getId(), "类目ID 不能为空");
            categoryService.delete(categoryDTO.getId(), modify);
            return success(DELETE_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Delete Category error，input error", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("Delete Category error，input error", e);
            throw new ApecRuntimeException("删除失败，请重试");
        }
    }

    /**
     * 根据 id 更新类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/updateCategory", method = RequestMethod.POST)
    public String updateCategory(@RequestBody String jsonStr)
    {
        logger.info("Update Category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            SettingCategoryDTO categoryDTO = getFormJSON(jsonStr, SettingCategoryDTO.class);
            Assert.hasLength(categoryDTO.getId(), "Id不能为空");
            categoryService.update(categoryDTO, modify);
            return success(UPDATE_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("更新Category 失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("更新失败", e);
            throw new ApecRuntimeException("更新失败");
        }
    }

    /**
     * 新增类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insertCategory", method = RequestMethod.POST)
    public String insertCategory(@RequestBody String jsonStr)
    {
        logger.info("Insert category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            SettingCategoryDTO categoryDTO = getFormJSON(jsonStr, SettingCategoryDTO.class);
            Assert.notNull(categoryDTO.getCategoryName(), "类目名称不能为空");
            categoryService.insert(categoryDTO, modify);
            return success(INSERT_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("添加失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("添加失败", e);
            throw new ApecRuntimeException("添加失败");
        }
    }

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/pageInfo", method = RequestMethod.POST)
    public String pageInfo(@RequestBody String jsonStr)
    {
        logger.info("Page info：{}", jsonStr);
        SettingInfoDTO settingDTO = getFormJSON(jsonStr, SettingInfoDTO.class);
        PageRequest pageRequest = genPageRequest(settingDTO);
        PageDTO<SettingInfoVO> result = settingService.page(settingDTO, pageRequest);
        return success(result);
    }

    /**
     * 根据 类目ID 进行删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/deleteInfo", method = RequestMethod.POST)
    public String deleteInfo(@RequestBody String jsonStr)
    {
        logger.info("Delete info：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            SettingInfoDTO settingDTO = getFormJSON(jsonStr, SettingInfoDTO.class);
            Assert.hasLength(settingDTO.getId(), "配置ID 不能为空");
            settingService.delete(settingDTO.getId(), modify);
            return success(DELETE_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Delete Info error，input error", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("Delete Info error，input error", e);
            throw new ApecRuntimeException("删除失败，请重试");
        }
    }

    /**
     * 根据 id 更新类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/updateInfo", method = RequestMethod.POST)
    public String updateInfo(@RequestBody String jsonStr)
    {
        logger.info("Update Info：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            SettingInfoDTO settingDTO = getFormJSON(jsonStr, SettingInfoDTO.class);
            Assert.hasLength(settingDTO.getId(), "Id不能为空");
            settingService.update(settingDTO, modify);
            return success(UPDATE_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("更新Info 失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("更新失败", e);
            throw new ApecRuntimeException("更新失败");
        }
    }

    /**
     * 新增类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insertInfo", method = RequestMethod.POST)
    public String insertInfo(@RequestBody String jsonStr)
    {
        logger.info("Insert info：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            SettingInfoDTO settingDTO = getFormJSON(jsonStr, SettingInfoDTO.class);
            Assert.notNull(settingDTO.getSettingKey(), "类目键不能为空");
            Assert.notNull(settingDTO.getSettingValue(), "类目值不能为空");
            Assert.notNull(settingDTO.getSettingName(), "类目名称不能为空");
            Assert.notNull(settingDTO.getSettingRemark(), "类目说明不能为空");
            settingService.insert(settingDTO, modify);
            return success(INSERT_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("添加失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("添加失败", e);
            throw new ApecRuntimeException("添加失败");
        }
    }

    /**
     * 查询
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "queryInfoBySettingKey", method = RequestMethod.POST)
    public String queryInfoBySettingKey(@RequestBody String jsonStr) {
        logger.info("queryInfoBySettingKey info：{}", jsonStr);
        SettingInfoDTO settingDTO = getFormJSON(jsonStr, SettingInfoDTO.class);
        SettingInfoVO settingInfoVO = settingService.queryInfoBySettingKey(settingDTO.getSettingKey());
        return success(settingInfoVO);
    }
}