package com.apec.setting.service.impl;

import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.information.dto.LabelDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.LabelVO;
import com.apec.setting.dao.LabelDAO;
import com.apec.setting.model.InformationLabelModel;
import com.apec.setting.model.QInformationLabelModel;
import com.apec.setting.service.LabelService;
import com.google.common.collect.Lists;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 16:05
 **/
@Service
public class LabelServiceImpl implements LabelService
{
    @Override
    public void insert(LabelDTO labelDTO, String modify)
    {
        BooleanExpression predicate = qLabel.orgNo.eq(labelDTO.getOrgNo());
        predicate = predicate.and(qLabel.labelName.eq(labelDTO.getLabelName()));
        boolean exist = labelDAO.exists(predicate);
        if(exist)
        {
            throw new InformationException("25016021");
        }
        InformationLabelModel label = new InformationLabelModel();
        BeanUtils.copyPropertiesIgnoreNullFilds(labelDTO, label);
        label.setId(Long.toString(idGen.nextId()));
        label.setCreateBy(modify);
        label.setLastUpdateBy(modify);
        Date now = new Date();
        label.setCreateDate(now);
        label.setLastUpdateDate(now);
        labelDAO.save(label);
    }

    @Override
    public List<LabelVO> list(String orgNo)
    {
        BooleanExpression predicate = qLabel.enableFlag.eq(EnableFlag.Y);
        if(StringUtil.isNotBlank(orgNo))
        {
            predicate = predicate.and(qLabel.orgNo.eq(orgNo));
        }
        List<InformationLabelModel> list = (List<InformationLabelModel>)labelDAO.findAll(predicate);
        List<LabelVO> result = Lists.newArrayListWithCapacity(list.size());
        list.forEach(label -> {
            LabelVO labelVO = new LabelVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(label, labelVO);
            result.add(labelVO);
        });
        return result;
    }

    private QInformationLabelModel qLabel = QInformationLabelModel.informationLabelModel;

    @Autowired
    private SnowFlakeKeyGen idGen;

    @Autowired
    private LabelDAO labelDAO;
}