package com.apec.setting.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.model.ResultData;
import com.apec.information.dto.SettingInfoDTO;
import com.apec.information.vo.SettingInfoVO;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/18 14:05
 **/

public interface SettingService
{
    /**
     * 增加
     * @param categoryDTO
     * @param modify
     */
    void insert(SettingInfoDTO settingDTO, String modify);

    /**
     * 分页查询
     * @param settingDTO
     * @return
     */
    PageDTO<SettingInfoVO> page(SettingInfoDTO settingDTO, PageRequest pageRequest);

    /**
     * 更新操作
     * @param settingDTO
     * @param modify
     */
    void update(SettingInfoDTO settingDTO, String modify);

    /**
     * 根据ID进行删除
     * @param id
     * @param modify
     */
    void delete(String id, String modify);

    /**
     * 查询类目下个数
     * @param categoryId
     * @return
     */
    Long count(String categoryId);

    /**
     * 查询类目下个数
     * @param categoryList
     * @return
     */
    Map<String, Long> count(List<String> categoryList);

    /**
     * 更新操作
     * @param categoryNo
     * @param categoryName
     * @param modify
     */
    void update(String categoryNo, String categoryName, String modify);

    /**
     * settingkey查询
     * @param settingKey
     * @return
     */
    Long findBySettingKey(String settingKey);

    /**
     * settingkey查询info
      * @param settingKey
     * @return
     */
    SettingInfoVO queryInfoBySettingKey(String settingKey);
}