package com.apec.setting.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.dto.SettingCategoryDTO;
import com.apec.information.vo.CategoryVO;
import com.apec.information.vo.SettingCategoryVO;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 14:05
 **/

public interface SettingCategoryService
{
    /**
     * 所有配置类型列表
     * @return
     */
    List<SettingCategoryVO> list();

    /**
     * 增加
     * @param categoryDTO
     */
    void insert(SettingCategoryDTO categoryDTO, String modify);

    /**
     * 分页查询
     * @param categoryDTO
     * @param pageRequest
     * @return
     */
    PageDTO<SettingCategoryVO> page(SettingCategoryDTO categoryDTO, PageRequest pageRequest);

    /**
     * 更新操作
     * @param categoryDTO
     */
    void update(SettingCategoryDTO categoryDTO, String modify);

    /**
     * 根据ID进行删除
     * @param id
     */
    void delete(String id, String modify);

    /**
     * 获取详情
     * @param categoryId
     * @return
     */
    CategoryVO detail(String categoryId);
}