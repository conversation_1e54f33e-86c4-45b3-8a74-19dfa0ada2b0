package com.apec.setting.service.impl;

import com.apec.framework.cache.CacheService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.dto.SettingCategoryDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.CategoryVO;
import com.apec.information.vo.SettingCategoryVO;
import com.apec.setting.dao.SettingCategoryDAO;
import com.apec.setting.model.InformationSettingCategoryModel;
import com.apec.setting.model.QInformationSettingCategoryModel;
import com.apec.setting.service.SettingCategoryService;
import com.apec.setting.service.SettingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.apec.information.constant.InformationConstants.CACHE_SETTING_CATEGORY;

/**
 * <AUTHOR>
 * @date 2018/7/18 16:05
 **/
@Service
public class SettingCategoryServiceImpl implements SettingCategoryService
{
    @Override
    public List<SettingCategoryVO> list()
    {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        List<InformationSettingCategoryModel> list = (List<InformationSettingCategoryModel>)categoryDAO
            .findAll(predicate);
        List<SettingCategoryVO> result = Lists.newArrayListWithCapacity(list.size());
        list.forEach(category -> {
            SettingCategoryVO vo = new SettingCategoryVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(category, vo);
            result.add(vo);
        });
        return result;
    }

    @Override
    public void insert(SettingCategoryDTO categoryDTO, String modify)
    {
        // Y && categoryName
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qCategory.categoryName.eq(categoryDTO.getCategoryName()));
        InformationSettingCategoryModel old = categoryDAO.findOne(predicate);
        if(null != old)
        {
            throw new InformationException("25016001");
        }
        Date now = new Date();
        InformationSettingCategoryModel category = new InformationSettingCategoryModel();
        BeanUtils.copyPropertiesIgnoreNullFilds(categoryDTO, category);
        category.setId(Long.toString(myGen.nextId()));
        category.setCreateBy(modify);
        category.setLastUpdateBy(modify);
        category.setCreateDate(now);
        category.setLastUpdateDate(now);
        categoryDAO.save(category);
    }

    @Override
    public PageDTO<SettingCategoryVO> page(SettingCategoryDTO categoryDTO, PageRequest pageRequest)
    {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        Page<InformationSettingCategoryModel> page = categoryDAO.findAll(predicate, pageRequest);
        List<SettingCategoryVO> row = toVO(page.getContent());
        PageDTO<SettingCategoryVO> result = new PageDTO<>();
        result.build(page.getTotalElements(), pageRequest, row);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SettingCategoryDTO categoryDTO, String modify)
    {
        cacheService.remove(CACHE_SETTING_CATEGORY + categoryDTO.getId());
        // Y && categoryName
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qCategory.categoryName.eq(categoryDTO.getCategoryName()));
        InformationSettingCategoryModel old = categoryDAO.findOne(predicate);
        if(null != old)
        {
            throw new InformationException("25016001");
        }
        InformationSettingCategoryModel category = categoryDAO.findOne(categoryDTO.getId());
        category.setCategoryName(categoryDTO.getCategoryName());
        category.setLastUpdateBy(modify);
        category.setLastUpdateDate(new Date());
        categoryDAO.saveAndFlush(category);
        settingService.update(category.getId(), category.getCategoryName(), modify);
    }

    @Override
    public void delete(String id, String modify)
    {
        Long count = settingService.count(id);
        if(count > 1)
        {
            throw new InformationException("25016002");
        }
        InformationSettingCategoryModel category = categoryDAO.findOne(id);
        category.setEnableFlag(EnableFlag.N);
        category.setLastUpdateBy(modify);
        category.setLastUpdateDate(new Date());
        categoryDAO.save(category);
    }

    @Override
    public CategoryVO detail(String id)
    {
        String key = CACHE_SETTING_CATEGORY + id;
        String value = cacheService.get(key);
        if(StringUtils.isNotBlank(value))
        {
            return JsonUtils.parseObject(value, CategoryVO.class);
        }
        InformationSettingCategoryModel category = categoryDAO.findOne(id);
        CategoryVO result = new CategoryVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(category, result);
        cacheService.add(key, JsonUtils.toJSONString(result), sessionTime);
        return result;
    }

    private QInformationSettingCategoryModel qCategory = QInformationSettingCategoryModel.informationSettingCategoryModel;

    @Value("${session.time.out}")
    private Integer sessionTime;

    @Autowired
    private SnowFlakeKeyGen myGen;

    @Autowired
    private SettingCategoryDAO categoryDAO;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private SettingService settingService;

    private List<SettingCategoryVO> toVO(List<InformationSettingCategoryModel> content)
    {
        if(CollectionUtils.isEmpty(content))
        {
            return Lists.newArrayList();
        }
        List<SettingCategoryVO> result = Lists.newArrayListWithCapacity(content.size());
        Map<String, SettingCategoryVO> map = Maps.newHashMapWithExpectedSize(content.size());
        List<String> ids = Lists.newArrayListWithCapacity(content.size());
        content.forEach(category -> {
            SettingCategoryVO vo = new SettingCategoryVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(category, vo);
            map.put(vo.getId(), vo);
            ids.add(vo.getId());
        });
        Map<String, Long> countMap = settingService.count(ids);
        ids.forEach(id -> {
            SettingCategoryVO vo = map.get(id);
            Long count = countMap.get(id);
            if(null == count)
            {
                count = 0L;
            }
            vo.setSettingCount(count);
            result.add(vo);
        });
        return result;
    }
}