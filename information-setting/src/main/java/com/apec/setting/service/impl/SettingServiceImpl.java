package com.apec.setting.service.impl;

import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.information.dto.SettingInfoDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.CategoryVO;
import com.apec.information.vo.SettingInfoVO;
import com.apec.setting.dao.SettingDAO;
import com.apec.setting.model.InformationSettingInfoModel;
import com.apec.setting.model.QInformationSettingInfoModel;
import com.apec.setting.service.SettingCategoryService;
import com.apec.setting.service.SettingService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/18 16:05
 **/
@Service
public class SettingServiceImpl implements SettingService
{
    @Override
    public void insert(SettingInfoDTO settingDTO, String modify)
    {
        Long count = findBySettingKey(settingDTO.getSettingKey());
        if (count > 0) {
            throw new InformationException("25016003");
        }
        // Y && settingName
        BooleanExpression predicate = qSetting.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qSetting.categoryId.eq(settingDTO.getCategoryId()));
        predicate = predicate.and(qSetting.settingKey.eq(settingDTO.getSettingKey()));
        InformationSettingInfoModel old = settingDAO.findOne(predicate);
        if(null != old)
        {
            throw new InformationException("25016001");
        }
        Date now = new Date();
        CategoryVO categoryVO = categoryService.detail(settingDTO.getCategoryId());
        InformationSettingInfoModel setting = new InformationSettingInfoModel();
        setting.setCategoryName(categoryVO.getCategoryName());
        BeanUtils.copyPropertiesIgnoreNullFilds(settingDTO, setting);
        setting.setId(Long.toString(myGen.nextId()));
        setting.setCreateBy(modify);
        setting.setLastUpdateBy(modify);
        setting.setCreateDate(now);
        setting.setLastUpdateDate(now);
        settingDAO.save(setting);
    }

    @Override
    public PageDTO<SettingInfoVO> page(SettingInfoDTO settingDTO, PageRequest pageRequest)
    {
        BooleanExpression predicate = qSetting.enableFlag.eq(EnableFlag.Y);
        if(StringUtils.isNotBlank(settingDTO.getCategoryId()))
        {
            predicate = predicate.and(qSetting.categoryId.eq(settingDTO.getCategoryId()));
        }
        Page<InformationSettingInfoModel> page = settingDAO.findAll(predicate, pageRequest);
        List<SettingInfoVO> row = toVO(page.getContent());
        PageDTO<SettingInfoVO> result = new PageDTO<>();
        result.build(page.getTotalElements(), pageRequest, row);
        return result;
    }

    @Override
    public void update(SettingInfoDTO settingDTO, String modify)
    {
        InformationSettingInfoModel setting = settingDAO.findOne(settingDTO.getId());
        if(null == setting)
        {
            throw new InformationException("25016011");
        }
        if(StringUtils.isNotBlank(settingDTO.getSettingName()))
        {
            setting.setSettingName(settingDTO.getSettingName());
        }
        if(StringUtils.isNotBlank(settingDTO.getSettingValue()))
        {
            setting.setSettingValue(settingDTO.getSettingValue());
        }
        if(StringUtils.isNotBlank(settingDTO.getSettingRemark()))
        {
            setting.setSettingRemark(settingDTO.getSettingRemark());
        }
        setting.setLastUpdateBy(modify);
        setting.setLastUpdateDate(new Date());
        settingDAO.save(setting);
    }

    @Override
    public void delete(String id, String modify)
    {
        InformationSettingInfoModel setting = settingDAO.findOne(id);
        if(null == setting)
        {
            throw new InformationException("25016011");
        }
        setting.setEnableFlag(EnableFlag.N);
        setting.setLastUpdateBy(modify);
        setting.setLastUpdateDate(new Date());
        settingDAO.save(setting);
    }

    @Override
    public Long count(String categoryId)
    {
        BooleanExpression predicate = qSetting.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qSetting.categoryId.eq(categoryId));
        Long result = settingDAO.count(predicate);
        return result;
    }

    @Override
    public Map<String, Long> count(List<String> categoryList)
    {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        map.put("categoryList", categoryList);
        Map<String, Long> result = Maps.newHashMapWithExpectedSize(categoryList.size());
        namedTemplate.query(QUERY_BY_CATEGORY, map, rs -> {
            result.put(rs.getString("CATEGORY_ID"), rs.getLong("COU"));
        });
        return result;
    }

    @Override
    public void update(String categoryNo, String categoryName, String modify)
    {
        Object[] obj = ArrayUtils.toArray(categoryName, modify, new Date(), categoryNo);
        jdbcTemplate.update(UPDATE_CATEGORY, obj);
    }

    @Override
    public Long findBySettingKey(String settingKey) {
        BooleanExpression predicate = qSetting.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qSetting.settingKey.eq(settingKey));
        Long result = settingDAO.count(predicate);
        return result;
    }

    @Override
    public SettingInfoVO queryInfoBySettingKey(String settingKey) {
        BooleanExpression predicate = qSetting.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qSetting.settingKey.eq(settingKey));
        InformationSettingInfoModel infoModel = settingDAO.findOne(predicate);
        SettingInfoVO vo = new SettingInfoVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(infoModel, vo);
        return vo;
    }


    private final static String UPDATE_CATEGORY = "UPDATE information_setting_info SET CATEGORY_NAME=?,LAST_UPDATE_BY=?,LAST_UPDATE_DATE=? WHERE CATEGORY_ID=?";

    private final static String QUERY_BY_CATEGORY = "SELECT CATEGORY_ID,COUNT(1) COU FROM information_setting_info WHERE ENABLE_FLAG='Y' AND CATEGORY_ID IN (:categoryList) GROUP BY CATEGORY_ID";

    private QInformationSettingInfoModel qSetting = QInformationSettingInfoModel.informationSettingInfoModel;

    @Autowired
    private SettingCategoryService categoryService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private NamedParameterJdbcTemplate namedTemplate;

    @Autowired
    private SnowFlakeKeyGen myGen;

    @Autowired
    private SettingDAO settingDAO;

    private List<SettingInfoVO> toVO(List<InformationSettingInfoModel> content)
    {
        List<SettingInfoVO> result = Lists.newArrayListWithCapacity(content.size());
        content.forEach(setting -> {
            SettingInfoVO vo = new SettingInfoVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(setting, vo);
            result.add(vo);
        });
        return result;
    }
}