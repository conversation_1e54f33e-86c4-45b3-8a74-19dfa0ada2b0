package com.apec.setting.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.dto.LabelDTO;
import com.apec.information.dto.SettingInfoDTO;
import com.apec.information.vo.LabelVO;
import com.apec.information.vo.SettingInfoVO;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/18 14:05
 **/

public interface LabelService
{
    /**
     * 新增标签
     * @param labelDTO
     * @param modify
     */
    void insert(LabelDTO labelDTO, String modify);

    /**
     * 查询标签列表
     * @param orgNo
     * @return
     */
    List<LabelVO> list(String orgNo);
}