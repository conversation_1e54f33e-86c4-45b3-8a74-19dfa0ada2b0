package com.apec.setting.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * <AUTHOR>
 * @date 2018-09-17
 **/
@Getter
@Setter
@ToString
@Entity
@Table(name = "information_setting_category")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationSettingCategoryModel extends BaseModel<String>
{
    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * 分类名称
     */
    @Column(name = "CATEGORY_NAME")
    private String categoryName;
}