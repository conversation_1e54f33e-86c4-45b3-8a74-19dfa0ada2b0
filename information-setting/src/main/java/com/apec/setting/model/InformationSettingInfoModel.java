package com.apec.setting.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * <AUTHOR>
 * @date 2018-09-17
 **/
@Getter
@Setter
@ToString
@Entity
@Table(name = "information_setting_info")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationSettingInfoModel extends BaseModel<String>
{
    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * 编号
     */
    @Column(name = "CATEGORY_ID")
    private String categoryId;

    /**
     * 编号
     */
    @Column(name = "CATEGORY_NAME")
    private String categoryName;

    /**
     * 键
     */
    @Column(name = "SETTING_KEY")
    private String settingKey;

    /**
     * 值
     */
    @Column(name = "SETTING_VALUE")
    private String settingValue;

    /**
     * 值
     */
    @Column(name = "SETTING_TYPE")
    private String settingType;

    /**
     * 展示名称
     */
    @Column(name = "SETTING_NAME")
    private String settingName;

    /**
     * 备注
     */
    @Column(name = "SETTING_REMARK")
    private String settingRemark;
}