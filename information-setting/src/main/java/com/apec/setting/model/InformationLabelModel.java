package com.apec.setting.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * <AUTHOR>
 * @date 2018-09-17
 **/
@Getter
@Setter
@ToString
@Entity
@Table(name = "information_label")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationLabelModel extends BaseModel<String>
{
    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * 标签名
     */
    @Column(name = "LABEL_NAME")
    private String labelName;

    /**
     * 标签颜色
     */
    @Column(name = "LABEL_COLOR")
    private String labelColor;
}