package com.apec.statistics.constant;

/**
 * @date 2018-10-09
 * <AUTHOR>
 */
public interface StatsConstants
{
    /**
     * 获取文章数量
     */
    String INFO_COUNT_SQL = "SELECT COUNT(DISTINCT INFORMATION_ID) TOTAL_COUNT,COUNT(CASE WHEN INFORMATION_SOURCE='1' THEN 1 END) INNER_COUNT FROM information_info WHERE CREATE_DATE BETWEEN ? AND ?";

    String STATS_SQL = "SELECT SUM(PAGE_VIEW) PV,SUM(UNIQUE_VIEW) UV,SUM(SHARE_COUNT) SHARE,SUM(DOWNLOAD_COUNT) DOWNLOAD,SUM(COMMENT_COUNT) COMMENT FROM information_info a WHERE STATS_DATE BETWEEN ? AND ?";

    String STATS_SQLS = "SELECT SUM(LIKE_COUNT) LIKED,SUM(COLLECT_COUNT) COLLECT FROM information_info a WHERE STATS_DATE = ?";

    String STATS_GRAPH = "SELECT STATS_DATE,SUM(PAGE_VIEW) PV,SUM(UNIQUE_VIEW) UV,SUM(SHARE_COUNT) SHARE,SUM(DOWNLOAD_COUNT) DOWNLOAD,SUM(COMMENT_COUNT) COMMENT,MAX(LIKE_COUNT) LIKED,MAX(COLLECT_COUNT) COLLECT FROM information_info a WHERE STATS_DATE BETWEEN ? AND ? GROUP BY STATS_DATE";

    String ORG_QUERY = " AND ORG_NO=?";

    String CATEGORY_QUERY = " AND CATEGORY_NO LIKE ?";

    String LABEL_QUERY = " AND INFORMATION_LABEL LIKE ?";

    String SELECT_SUMMARY_COUNT = "SELECT COUNT(DISTINCT INFORMATION_ID) COU FROM information_info WHERE ORG_NO=?";

    String SELECT_SUMMARY_DATA = "SELECT INFORMATION_ID ID,INFORMATION_TITLE,INFORMATION_SUMMARY,SUM(PAGE_VIEW) PAGE_VIEW,SUM(UNIQUE_VIEW) UNIQUE_VIEW,SUM(SHARE_COUNT) SHARE_COUNT,SUM(COMMENT_COUNT) COMMENT_COUNT,SUM(LIKE_COUNT) LIKE_COUNT,SUM(COLLECT_COUNT) COLLECT_COUNT FROM information_info WHERE ORG_NO=? GROUP BY INFORMATION_ID";

}