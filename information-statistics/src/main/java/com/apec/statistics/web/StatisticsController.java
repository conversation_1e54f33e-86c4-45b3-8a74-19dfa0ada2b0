package com.apec.statistics.web;

import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.base.MyController;
import com.apec.information.dto.HomeDTO;
import com.apec.information.dto.HomeInfoDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.vo.HomeBaseVO;
import com.apec.information.vo.HomeInfoVO;
import com.apec.information.vo.HomeVO;
import com.apec.statistics.service.StatisticsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 20:09
 **/
@RestController
@RequestMapping("/stats")
public class StatisticsController extends MyController
{
    private static final Logger logger = LoggerFactory.getLogger(StatisticsController.class);

    private static final Long TWO_YEAR = 946656000000L;

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/home", method = RequestMethod.POST)
    public String home(@RequestBody String jsonStr)
    {
        logger.info("Get home：{}", jsonStr);
        HomeDTO homeDTO = getFormJSON(jsonStr, HomeDTO.class);
        if(null == homeDTO.getStartDate() || null == homeDTO.getEndDate())
        {
            homeDTO.setStartDate(new Date(TWO_YEAR));
            homeDTO.setEndDate(new Date());
        }
        HomeVO result = statisticsService.home(homeDTO);
        return success(result);
    }

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public String list(@RequestBody String jsonStr)
    {
        logger.info("Get list：{}", jsonStr);
        HomeDTO homeDTO = getFormJSON(jsonStr, HomeDTO.class);
        if(null == homeDTO.getStartDate() || null == homeDTO.getEndDate())
        {
            throw new InformationException("25010001");
        }
        List<HomeBaseVO> result = statisticsService.list(homeDTO);
        return success(result);
    }

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/infoList", method = RequestMethod.POST)
    public String infoList(@RequestBody String jsonStr)
    {
        logger.info("Get info list：{}", jsonStr);
        HomeInfoDTO homeDTO = getFormJSON(jsonStr, HomeInfoDTO.class);
        if(StringUtils.isBlank(homeDTO.getOrgNo()))
        {
            throw new InformationException("25010001");
        }
        PageRequest request = getPageRequestSort(homeDTO);
        PageDTO<HomeInfoVO> result = statisticsService.infoList(homeDTO, request);
        return success(result);
    }
}