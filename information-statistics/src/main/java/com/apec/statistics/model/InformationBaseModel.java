package com.apec.statistics.model;

import com.apec.framework.jpa.model.BaseModel;
import com.apec.information.util.ArrayDateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 资讯无状态记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class InformationBaseModel extends BaseModel<String>
{
    private String createDateString;

    @Override
    public void setCreateDate(Date createDate)
    {
        super.setCreateDate(createDate);
        this.createDateString = ArrayDateUtil.ymdToString(createDate);
    }
}