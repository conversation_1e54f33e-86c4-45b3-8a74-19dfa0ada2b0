package com.apec.statistics.model;

import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.enumtype.EnableFlag;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.util.Date;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * 资讯记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "information_info")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationModels
{
    @Id
    @GeneratedValue(generator = FrameConsts.SYSTEM_GENERATOR)
    @Column(name = "ID")
    private String id;

    @Column(name = "STATS_DATE")
    private Date statsDate;

    @Column(name = "ORG_NO")
    private String orgNo;

    @Column(name = "CATEGORY_NO")
    private String categoryNo;

    @Column(name = "INFORMATION_ID")
    private String informationId;

    @Column(name = "INFORMATION_SOURCE")
    private String informationSource;

    @Column(name = "INFORMATION_TITLE")
    private String informationTitle;

    @Column(name = "INFORMATION_SUMMARY")
    private String informationSummary;

    @Column(name = "INFORMATION_LABEL")
    private String informationLabel;

    @Column(name = "PAGE_VIEW")
    private Integer pageView = 0;

    @Column(name = "UNIQUE_VIEW")
    private Integer uniqueView = 0;

    @Column(name = "SHARE_COUNT")
    private Integer shareCount = 0;

    @Column(name = "DOWNLOAD_COUNT")
    private Integer downloadCount = 0;

    @Column(name = "COMMENT_COUNT")
    private Integer commentCount = 0;

    @Column(name = "LIKE_COUNT")
    private Integer likeCount = 0;

    @Column(name = "COLLECT_COUNT")
    private Integer collectCount = 0;

    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss", timezone = "GMT+8")
    @Column(name = "CREATE_DATE", updatable = false)
    private Date createDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "ENABLE_FLAG")
    private EnableFlag enableFlag = EnableFlag.Y;

    public void setPageView(Integer pageView)
    {
        this.pageView = null != pageView ? pageView : 0;
    }

    public void setUniqueView(Integer uniqueView)
    {
        this.uniqueView = null != uniqueView ? uniqueView : 0;
    }

    public void setShareCount(Integer shareCount)
    {
        this.shareCount = null != shareCount ? shareCount : 0;
    }

    public void setDownloadCount(Integer downloadCount)
    {
        this.downloadCount = null != downloadCount ? downloadCount : 0;
    }

    public void setCommentCount(Integer commentCount)
    {
        this.commentCount = null != commentCount ? commentCount : 0;
    }

    public void setLikeCount(Integer likeCount)
    {
        this.likeCount = null != likeCount ? likeCount : 0;
    }

    public void setCollectCount(Integer collectCount)
    {
        this.collectCount = null != collectCount ? collectCount : 0;
    }

}