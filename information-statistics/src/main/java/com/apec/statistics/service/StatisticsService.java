package com.apec.statistics.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.dto.HomeInfoDTO;
import com.apec.information.vo.HomeBaseVO;
import com.apec.information.vo.HomeInfoVO;
import com.apec.information.vo.HomeVO;
import com.apec.information.dto.HomeDTO;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 14:05
 **/

public interface StatisticsService
{
    /**
     * 获取首页信息
     * @param homeDTO
     * @return
     */
    HomeVO home(HomeDTO homeDTO);

    /**
     * 获取首页下面的信息
     * @param homeDTO
     * @return
     */
    List<HomeBaseVO> list(HomeDTO homeDTO);

    /**
     * 获取子公司首页下面的信息
     * @param homeDTO
     * @param request
     * @return
     */
    PageDTO<HomeInfoVO> infoList(HomeInfoDTO homeDTO, PageRequest request);
}