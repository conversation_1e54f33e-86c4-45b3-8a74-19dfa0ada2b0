package com.apec.statistics.service.impl;

import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.information.dto.HomeDTO;
import com.apec.information.dto.HomeInfoDTO;
import com.apec.information.util.ArrayDateUtil;
import com.apec.information.vo.HomeBaseVO;
import com.apec.information.vo.HomeInfoVO;
import com.apec.information.vo.HomeVO;
import com.apec.statistics.dao.InfoDAO;
import com.apec.statistics.model.QInformationModels;
import com.apec.statistics.service.StatisticsService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.apec.statistics.constant.StatsConstants.*;

/**
 * <AUTHOR>
 * @date 2018/7/18 16:05
 **/
@Service
public class StatisticsServiceImpl implements StatisticsService
{
    @Override
    public HomeVO home(HomeDTO homeDTO)
    {
        HomeVO result = new HomeVO();
        StringBuilder sb = new StringBuilder(INFO_COUNT_SQL);
        StringBuilder sb2 = new StringBuilder(STATS_SQL);
        StringBuilder sb3 = new StringBuilder(STATS_SQLS);
        List<Object> list = Lists.newArrayList();
        list.add(homeDTO.getStartDate());
        list.add(homeDTO.getEndDate());
        if(StringUtils.isNotBlank(homeDTO.getOrgNo()))
        {
            sb.append(ORG_QUERY);
            sb2.append(ORG_QUERY);
            sb3.append(ORG_QUERY);
            list.add(homeDTO.getOrgNo());
        }
        if(StringUtils.isNotBlank(homeDTO.getCategoryNo()))
        {
            sb.append(CATEGORY_QUERY);
            sb2.append(CATEGORY_QUERY);
            sb3.append(CATEGORY_QUERY);
            list.add("%" + homeDTO.getCategoryNo() + "%");
        }
        if(StringUtils.isNotBlank(homeDTO.getLabel()))
        {
            sb.append(LABEL_QUERY);
            sb2.append(LABEL_QUERY);
            sb3.append(LABEL_QUERY);
            list.add("%" + homeDTO.getLabel() + "%");
        }
        Object[] obj = list.toArray();
        jdbcTemplate.query(sb.toString(), obj, rs -> {
            result.setInformationCount(rs.getLong("TOTAL_COUNT"));
            result.setOriginalInformationCount(rs.getLong("INNER_COUNT"));
        });
        jdbcTemplate.query(sb2.toString(), obj, rs -> {
            result.setPageView(rs.getLong("PV"));
            result.setUniqueView(rs.getLong("UV"));
            result.setShareCount(rs.getLong("SHARE"));
            result.setDownloadPageView(rs.getLong("DOWNLOAD"));
            result.setCommentCount(rs.getLong("COMMENT"));
        });
        list.remove(0);
        jdbcTemplate.query(sb3.toString(), list.toArray(), rs -> {
            result.setLikeCount(rs.getLong("LIKED"));
            result.setCollectCount(rs.getLong("COLLECT"));
        });
        return result;
    }

    @Override
    public List<HomeBaseVO> list(HomeDTO homeDTO)
    {
        List<String> dateList = ArrayDateUtil.get(homeDTO.getStartDate(), homeDTO.getEndDate());
        List<HomeBaseVO> result = Lists.newArrayListWithCapacity(dateList.size());
        Map<String, HomeBaseVO> map = Maps.newHashMap();
        jdbcTemplate.query(STATS_GRAPH, ArrayUtils.toArray(homeDTO.getStartDate(), homeDTO.getEndDate()), rs -> {
            HomeBaseVO homeBaseVO = new HomeBaseVO();
            homeBaseVO.setStatsDate(rs.getString("STATS_DATE"));
            homeBaseVO.setPageView(rs.getLong("PV"));
            homeBaseVO.setUniqueView(rs.getLong("UV"));
            homeBaseVO.setShareCount(rs.getLong("SHARE"));
            homeBaseVO.setDownloadPageView(rs.getLong("DOWNLOAD"));
            homeBaseVO.setCommentCount(rs.getLong("COMMENT"));
            homeBaseVO.setCollectCount(rs.getLong("COLLECT"));
            map.put(homeBaseVO.getStatsDate(), homeBaseVO);
        });
        for(String date : dateList)
        {
            HomeBaseVO baseVO = map.get(date);
            if(null == baseVO)
            {
                baseVO = new HomeBaseVO();
                baseVO.setStatsDate(date);
            }
            result.add(baseVO);
        }
        return result;
    }

    @Override
    public PageDTO<HomeInfoVO> infoList(HomeInfoDTO homeDTO, PageRequest request)
    {
        // 获取总数
        Long count = jdbcTemplate.queryForObject(SELECT_SUMMARY_COUNT,
                                                 ArrayUtils.toArray(homeDTO.getOrgNo()),
                                                 (rs, rowNum) -> rs.getLong("COU"));
        if(null == count || 1 > count)
        {
            return null;
        }

        StringBuilder sb = new StringBuilder(SELECT_SUMMARY_DATA);
        String sortColumn = homeDTO.getSortColumn();
        if(StringUtils.isBlank(sortColumn))
        {
            sortColumn = "PAGE_VIEW";
        }
        sb.append(" ORDER BY ").append(sortColumn).append(" LIMIT ?,?");
        Integer skipNumber = (homeDTO.getCurrentNo() - 1) * homeDTO.getPageSize();

        // 生成返回实体
        PageDTO<HomeInfoVO> result = new PageDTO<>();
        Object[] obj = ArrayUtils.toArray(homeDTO.getOrgNo(), skipNumber, homeDTO.getPageSize());
        List<HomeInfoVO> rows = jdbcTemplate.query(sb.toString(), obj, new BeanPropertyRowMapper<>(HomeInfoVO.class));

        result.build(count, request, rows);
        return result;
    }

    private Logger logger = LoggerFactory.getLogger(StatisticsServiceImpl.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private InfoDAO infoDAO;
}