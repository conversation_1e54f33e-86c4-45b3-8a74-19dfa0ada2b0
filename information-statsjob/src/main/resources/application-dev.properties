logging.level.com.apec=DEBUG
workerId=1

eureka.client.serviceUrl.defaultZone=http://*************:1111/eureka/

spring.datasource.primary.url=*************************************************************************************************************************
spring.datasource.primary.username=root
spring.datasource.primary.password=Crm_2018

redis.database=0
redis.host=*************
redis.port=9001
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=*************
spring.redis.port=9001
spring.redis.host2=*************
spring.redis.port2=9002
spring.redis.timeout=5000
spring.redis.maxRedirections=5

spring.rabbitmq.host=*************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

# mongodb setting
spring.data.mongodb.uri=mongodb://*************:27017/information