package com.apec.statistics.service;

import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.util.ArrayDateUtil;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.statistics.dao.InfoDAO;
import com.apec.statistics.model.InformationModel;
import com.apec.statistics.model.InformationModels;
import com.apec.statistics.model.QInformationModels;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.invoke.MethodHandles;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.apec.statistics.constant.StatsConstants.STATS_CLICK;
import static com.apec.statistics.constant.StatsConstants.STATS_UV;

/**
 * @date 2018-10-10
 * <AUTHOR>
 */
@Component
public class ScheduledService
{
    /**
     * 每五分钟同步资讯列表
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void syncInformationNow()
    {
        Date startDate = ArrayDateUtil.thisDay();
        Date endDate = new Date();
        information(startDate, endDate);
    }

    /**
     * 每日同步上一日资讯列表
     */
    @Scheduled(cron = "0 1 0 * * ?")
    public void syncInformationDay()
    {
        Date endDate = ArrayDateUtil.thisDay();
        Date startDate = ArrayDateUtil.nextDate(endDate, -1);
        information(startDate, endDate);
    }

    /**
     * 日志句柄
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private JdbcTemplate template;

    @Autowired
    private InfoDAO infoDAO;

    @Autowired
    private SnowFlakeKeyGen keygen;

    private final static String INFORMATION = "INFORMATION_ID";

    private final static String COUNT = "COU";

    private final static String CLICK_TYPE = "CLICK_TYPE";

    private final static String ORG_UV_GROUP_BY = "{$group:{_id:'$informationId',count:{$sum:1}}}";

    private final static DBObject DB_ORG_UV_GROUP_BY = (DBObject)JSON.parse(ORG_UV_GROUP_BY);

    private String[] IGNORE_FIELDS = ArrayUtils.toArray("id", "informationLabel");

    private final Integer year = -30;

    private void information(Date startDate, Date endDate)
    {
        Date lastYear = ArrayDateUtil.nextDate(endDate, year);
        LOG.debug("//// 本次将同步{} 到 {} 的资讯数据", JsonUtils.toJSONString(startDate), JsonUtils.toJSONString(endDate));
        Query query = Query.query(Criteria.where("lastInteractTimestamp").lt(endDate.getTime()).gt(lastYear.getTime()));
        List<InformationModel> infoList = mongoTemplate.find(query, InformationModel.class);
        LOG.debug("//// 查询出 {} 条资讯", infoList.size());
        Object[] obj = ArrayUtils.toArray(startDate, endDate);
        // 获取pv
        Map<String, Integer> pvMap = Maps.newHashMap();
        Map<String, Integer> shareMap = Maps.newHashMap();
        Map<String, Integer> downloadMap = Maps.newHashMap();
//        template.query(STATS_CLICK, obj, rs -> {
//            switch (rs.getString(CLICK_TYPE))
//            {
//                case "share":
//                    shareMap.put(rs.getString(INFORMATION), rs.getInt(COUNT));
//                    break;
//                case "download":
//                    downloadMap.put(rs.getString(INFORMATION), rs.getInt(COUNT));
//                    break;
//                case "information":
//                default:
//                    pvMap.put(rs.getString(INFORMATION), rs.getInt(COUNT));
//                    break;
//            }
//        });
//        LOG.debug("//// 查询出 {} 条分享， {} 条下载， {} 条pv", shareMap.size(), downloadMap.size(), pvMap.size());
        Map<String, Integer> uvMap = Maps.newHashMap();
//        template.query(STATS_UV, obj, (rs, rowNum) -> uvMap.put(rs.getString(INFORMATION), rs.getInt(COUNT)));
//        LOG.debug("//// 查询出 {} 条uv", uvMap.size());
        // 获取评论
        Map<String, Integer> commentMap = mongoComment(startDate, endDate);
        LOG.debug("//// 查询出 {} 条评论", commentMap.size());

        next(startDate, pvMap, shareMap, downloadMap, uvMap, commentMap, infoList);
    }

    private void next(Date startDate, Map<String, Integer> pvMap, Map<String, Integer> shareMap,
        Map<String, Integer> downloadMap, Map<String, Integer> uvMap, Map<String, Integer> commentMap,
        List<InformationModel> infoList)
    {
        LOG.debug("//// 同步资讯", infoList.size());
        BooleanExpression predicate = QInformationModels.informationModels.statsDate.eq(startDate);
        for(InformationModel information : infoList)
        {
            String informationId = information.getId();
            BooleanExpression temp = predicate
                .and(QInformationModels.informationModels.informationId.eq(informationId));
            InformationModels info = getInfo(temp, information);
            info.setStatsDate(startDate);
            info.setInformationId(informationId);
            info.setPageView(pvMap.remove(informationId));
            info.setUniqueView(uvMap.remove(informationId));
            info.setShareCount(shareMap.remove(informationId));
            info.setCommentCount(commentMap.remove(informationId));
            info.setDownloadCount(downloadMap.remove(informationId));
            info.setCollectCount(null == information.getCollectList() ? 0 : information.getCollectList().size());
            info.setLikeCount(null == information.getLikeList() ? 0 : information.getLikeList().size());
            if(CollectionUtils.isNotEmpty(information.getInformationLabel()))
            {
                info.setInformationLabel(JsonUtils.toJSONString(information.getInformationLabel()));
            }
            infoDAO.save(info);
        }
        LOG.debug("//// 同步{}条资讯成功", infoList.size());
        Set<String> set = Sets.newHashSet(pvMap.keySet());
        set.addAll(uvMap.keySet());
        set.addAll(shareMap.keySet());
        set.addAll(commentMap.keySet());
        set.addAll(downloadMap.keySet());
        next(startDate, set, pvMap, uvMap, shareMap, commentMap, downloadMap);
    }

    private void next(Date startDate, Set<String> set, Map<String, Integer> pvMap, Map<String, Integer> uvMap,
        Map<String, Integer> shareMap, Map<String, Integer> commentMap, Map<String, Integer> downloadMap)
    {
        Query query = Query.query(Criteria.where("id").in(set));
        List<InformationModel> infoList = mongoTemplate.find(query, InformationModel.class);
        BooleanExpression predicate = QInformationModels.informationModels.statsDate.eq(startDate);
        for(InformationModel information : infoList)
        {
            String informationId = information.getId();
            BooleanExpression temp = predicate
                .and(QInformationModels.informationModels.informationId.eq(informationId));
            InformationModels info = getInfo(temp, information);
            info.setStatsDate(startDate);
            info.setInformationId(informationId);
            info.setPageView(pvMap.get(informationId));
            info.setUniqueView(uvMap.get(informationId));
            info.setShareCount(shareMap.get(informationId));
            info.setCommentCount(commentMap.get(informationId));
            info.setDownloadCount(downloadMap.get(informationId));
            info.setCollectCount(null == information.getCollectList() ? 0 : information.getCollectList().size());
            info.setLikeCount(null == information.getLikeList() ? 0 : information.getLikeList().size());
            if(CollectionUtils.isNotEmpty(information.getInformationLabel()))
            {
                info.setInformationLabel(JsonUtils.toJSONString(information.getInformationLabel()));
            }
            infoDAO.save(info);
        }
        LOG.debug("//// 同步{}条资讯成功", infoList.size());
    }

    private Map<String, Integer> mongoComment(Date startDate, Date endDate)
    {
        Query query = Query.query(Criteria.where("createDate").gt(startDate).lt(endDate));
        DBObject match = new BasicDBObject("$match", query.getQueryObject());
        // match
        List<DBObject> list = Lists.newArrayList(match);
        // group
        list.add(DB_ORG_UV_GROUP_BY);

        //从mongodb获取数据
        LOG.info("mongodb sql : {}", JsonUtils.toJSONString(list));
        AggregationOutput output = mongoTemplate.getCollection("information_comment_model").aggregate(list);
        List<DBObject> results = (List<DBObject>)output.results();
        Map<String, Integer> result = Maps.newHashMap();
        results.forEach(dbObject -> result.put((String)dbObject.get("_id"), (Integer)dbObject.get("count")));
        return result;
    }

    private InformationModels getInfo(BooleanExpression predicate, InformationModel information)
    {
        InformationModels oldInfo = infoDAO.findOne(predicate);
        InformationModels info = null == oldInfo ? new InformationModels() : oldInfo;
        BeanUtils.copyPropertiesIgnoreNullFilds(information, info, IGNORE_FIELDS);
        if(null == info.getId())
        {
            info.setId(Long.toString(keygen.nextId()));
        }
        return info;
    }
}
