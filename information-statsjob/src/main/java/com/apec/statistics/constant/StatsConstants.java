package com.apec.statistics.constant;

/**
 * @date 2018-10-09
 * <AUTHOR>
 */
public interface StatsConstants
{
    String STATS_CLICK = "SELECT INFORMATION_ID,CLICK_TYPE,COUNT(1) COU FROM information_click WHERE CREATE_DATE BETWEEN ? AND ? GROUP BY INFORMATION_ID,CLICK_TYPE";

    String STATS_UV = "SELECT INFORMATION_ID,COUNT(DISTINCT BROWSER_UUID) COU FROM information_click WHERE CLICK_TYPE='information' AND CREATE_DATE BETWEEN ? AND ? GROUP BY INFORMATION_ID";

    String STATS_LIKE = "SELECT INFORMATION_ID,COUNT(1) COU FROM information_login WHERE LIKED=1 AND LIKE_DATE BETWEEN ? AND ? GROUP BY INFORMATION_ID";

    String STATS_COLLECT = "SELECT INFORMATION_ID,COUNT(1) COU FROM information_login WHERE COLLECT=1 AND COLLECT_DATE BETWEEN ? AND ? GROUP BY INFORMATION_ID";

    /**
     * 获取文章数量
     */
    String INFO_COUNT_SQL = "SELECT COUNT(1) TOTAL_COUNT,COUNT(CASE WHEN INFORMATION_SOURCE THEN 1 END) INNER_COUNT FROM information_info WHERE CREATE_DATE BETWEEN ? AND ?";

    String STATS_SQL = "SELECT SUM(PAGE_VIEW) PV,SUM(UNIQUE_VIEW) UV,SUM(SHARE_COUNT) SHARE,SUM(DOWNLOAD_COUNT) DOWNLOAD,SUM(COMMENT_COUNT) COMMENT,SUM(LIKE_COUNT) LIKED,SUM(COLLECT_COUNT) COLLECT FROM information_stats a WHERE STATS_DATE BETWEEN ? AND ?";

    String STATS_GRAPH = "SELECT STATS_DATE,SUM(PAGE_VIEW) PV,SUM(UNIQUE_VIEW) UV,SUM(SHARE_COUNT) SHARE,SUM(DOWNLOAD_COUNT) DOWNLOAD,SUM(COMMENT_COUNT) COMMENT,SUM(LIKE_COUNT) LIKED,SUM(COLLECT_COUNT) COLLECT FROM information_stats a WHERE STATS_DATE BETWEEN ? AND ? GROUP BY STATS_DATE";

    String ORG_QUERY = " AND ORG_NO=?";

    String CATEGORY_QUERY = " AND CATEGORY_NO=?";

    String LABEL_QUERY = " AND INFORMATION_LABEL LIKE ?";

    String SELECT_SUMMARY_DATA = "SELECT a.ID,INFORMATION_TITLE,INFORMATION_SUMMARY,COUNT(PAGE_VIEW) PAGE_VIEW,COUNT(UNIQUE_VIEW) UNIQUE_VIEW,COUNT(SHARE_COUNT) SHARE_COUNT,COUNT(COMMENT_COUNT) COMMENT_COUNT,COUNT(LIKE_COUNT) LIKE_COUNT,COUNT(COLLECT_COUNT) COLLECT_COUNT FROM information_info a LEFT JOIN information_stats b ON a.ID=b.`INFORMATION_ID` WHERE a.ORG_NO=? GROUP BY a.`ID`";

}