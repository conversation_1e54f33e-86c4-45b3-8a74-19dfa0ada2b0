package com.apec.statistics.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 资讯记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Document(collection = "information_model")
public class InformationModel extends InformationBaseModel
{
    /**
     * 组织编号
     */
    private String orgNo;

    /**
     * 类目编号
     */
    private String categoryNo;

    /**
     * 模板（1:文章、2:快讯）
     */
    private Integer informationModule;

    /**
     * 编号
     */
    private String informationNo;

    /**
     * 发布状态
     */
    private String informationPublicStatus;

    /**
     * 发布源
     */
    private String informationSource;

    /**
     * 发布源名称
     */
    private String informationSourceName;

    /**
     * 发布源链接
     */
    private String informationSourceLink;

    /**
     * 编辑状态
     */
    private Boolean informationEditable;

    /**
     * 页面名称
     */
    private String informationName;

    /**
     * 页面标题名称
     */
    private String informationTitle;

    /**
     * 发布人
     */
    private String informationIssuer;

    /**
     * 摘要
     */
    private String informationSummary;

    /**
     * 内容
     */
    private String informationContent;

    /**
     * 缩略图
     */
    private String informationCoverImage;

    /**
     * 所有图列表
     */
    private List<String> informationImageList;

    /**
     * 标签列表
     */
    private List<String> informationLabel;

    /**
     * 备注
     */
    private String informationRemark;

    /**
     * 收藏人id
     */
    private Set<String> collectList;

    /**
     * 喜欢人id
     */
    private Set<String> likeList;

    /**
     * 最后互动时间
     */
    private Long lastInteractTimestamp;
}