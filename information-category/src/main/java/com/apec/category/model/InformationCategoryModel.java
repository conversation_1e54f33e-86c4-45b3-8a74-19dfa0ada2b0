package com.apec.category.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * <AUTHOR>
 * @date 2018-09-17
 **/
@Getter
@Setter
@ToString
@Entity
@Table(name = "information_category")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationCategoryModel extends BaseModel<String>
{
    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * 编号
     */
    @Column(name = "CATEGORY_NO")
    private String categoryNo;

    /**
     * 分类名称
     */
    @Column(name = "CATEGORY_NAME")
    private String categoryName;

    /**
     * 父组织
     */
    @Column(name = "PARENT_NO")
    private String parentNo;

    /**
     * 选用模块(1：普通文章，2：快讯)
     */
    @Column(name = "MODULE")
    private Integer module;

    /**
     * 路径
     */
    @Column(name = "PATH")
    private String path;

    /**
     * 排序
     */
    @Column(name = "SORT")
    private Integer sort;

    /**
     * 是否默认
     */
    @Column(name = "def", columnDefinition = "int(11) COMMENT '是否默认 0：默认 1：不默认'")
    private Integer def;

    /**
     * app可见
     */
    @Column(name = "visiable_app", columnDefinition = "int(11) COMMENT 'app是否可见 0：可见 1：不可见'")
    private Integer visiableApp;

    /**
     * 后台可见
     */
    @Column(name = "visiable_mana", columnDefinition = "int(11) COMMENT '后台是否可见 0：可见 1：不可见'")
    private Integer visiableMana;

    /**
     * 外部编号
     */
    @Column(name = "external_no")
    private String externalNo;

    /**
     * 商家编号
     */
    @Column(name = "merchant_id")
    private String merchantId;

}