package com.apec.category.web;


import com.apec.category.service.CategoryApiService;
import com.apec.framework.base.BaseController;
import com.apec.information.dto.CategoryDTO;
import com.apec.information.vo.CategoryVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.apec.information.constant.InformationConstants.SUCCESS_BOOL;
import static com.apec.information.constant.InformationConstants.SUCCESS_STATUS;

/**
 * <AUTHOR>
 * @description 分类的接口
 * @date 2018/7/18 20:09
 **/
@RestController
@RequestMapping("/api")
public class CategoryApiController extends BaseController {


    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);

    @Value("${default.category.node}")
    private String defaultNode;

    @Autowired
    private CategoryApiService categoryApiService;

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public String query(@RequestBody String jsonStr)
    {
        logger.info("Query category：{}", jsonStr);
        CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
        if(StringUtils.isBlank(categoryDTO.getOrgNo()))
        {
            categoryDTO.setOrgNo(defaultNode);
        }
        List<CategoryVO> result = categoryApiService.query(categoryDTO.getOrgNo());
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }
}
