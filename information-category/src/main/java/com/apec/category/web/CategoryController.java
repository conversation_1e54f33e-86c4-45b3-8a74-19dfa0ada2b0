package com.apec.category.web;

import com.apec.information.dto.CategoryDTO;
import com.apec.category.service.CategoryService;
import com.apec.information.dto.CategoryStatusDTO;
import com.apec.information.vo.CategoryVO;
import com.apec.framework.base.BaseController;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.information.constant.InformationConstants;
import com.apec.information.handler.InformationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @description 分类的接口
 * @date 2018/7/18 20:09
 **/
@RestController
@RequestMapping("/category")
public class CategoryController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);

    @Value("${default.category.node}")
    private String defaultNode;

    @Autowired
    private CategoryService categoryService;

    /**
     * 根据 组织编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public String query(@RequestBody String jsonStr)
    {
        logger.info("Query category：{}", jsonStr);
        CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
        if(StringUtils.isBlank(categoryDTO.getOrgNo())){
            categoryDTO.setOrgNo(defaultNode);
        }
        List<CategoryVO> result = categoryService.query(categoryDTO.getOrgNo());
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }

    /**
     * 根据 类目ID 进行删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public String delete(@RequestBody String jsonStr)
    {
        logger.info("Delete category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
            Assert.hasLength(categoryDTO.getId(), "类目ID 不能为空");
            categoryService.delete(categoryDTO.getId(), modify);
            return getResultJSONStr(SUCCESS_BOOL, DELETE_SUCCESS, SUCCESS_STATUS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Delete InformationCategoryModel error，input error", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("Delete InformationCategoryModel error，input error", e);
            throw new ApecRuntimeException("删除失败，请重试");
        }
    }

    /**
     * 根据 id 更新类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public String update(@RequestBody String jsonStr)
    {
        logger.info("Update Category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
            Assert.hasLength(categoryDTO.getId(), "Id不能为空");
            categoryDTO.setMerchantId(StringUtils.join(categoryDTO.getMerchantIds(), ","));
            categoryService.update(categoryDTO, modify);
            return getResultJSONStr(SUCCESS_BOOL, UPDATE_SUCCESS, SUCCESS_STATUS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("更新Category 失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL, e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("更新失败", e);
            throw new ApecRuntimeException("更新失败");
        }
    }

    /**
     * 新增类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    public String insert(@RequestBody String jsonStr)
    {
        logger.info("Insert category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
            Assert.notNull(categoryDTO.getCategoryName(), "类目名称不能为空");
            Assert.notNull(categoryDTO.getModule(), "类目可用模板不能为空");
            Assert.notNull(categoryDTO.getParentNo(), "父类目编号不能为空");
            if(null == categoryDTO.getSort())
            {
                categoryDTO.setSort(DEFAULT_SORT);
            }
            if (null == categoryDTO.getDef()) {
                categoryDTO.setDef(DEFAULT_DEF);
            }
            if (null == categoryDTO.getVisiableMana()) {
                categoryDTO.setVisiableMana(DEFAULT_VISIABLE_MANA);
            }
            if (null == categoryDTO.getVisiableApp()) {
                categoryDTO.setVisiableApp(DEFAULT_VISIABLE_APP);
            }
            if(!CollectionUtils.isEmpty(categoryDTO.getMerchantIds())){
                categoryDTO.setMerchantId(StringUtils.join(categoryDTO.getMerchantIds(), ","));
            }
                categoryService.insert(categoryDTO, modify);
            return getResultJSONStr(SUCCESS_BOOL, INSERT_SUCCESS, SUCCESS_STATUS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("添加失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL, e.getMessage());
        }
        catch (InformationException e)
        {
            logger.error("添加失败", e);
            throw new InformationException(e.getErrorCode(), e.getArgs());
        }
        catch (Exception e)
        {
            logger.error("添加失败", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL, e.getMessage());
        }
    }

    /**
     * 根据 类目编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/queryByCategoryNo", method = RequestMethod.POST)
    public String queryByCategoryNo(@RequestBody String jsonStr)
    {
        logger.info("Query category：{}", jsonStr);
        CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
        List<CategoryVO> result = categoryService.queryByCategoryNo(categoryDTO.getCategoryNo(),
                categoryDTO.getMerchantId());
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }

    @RequestMapping(value = "/changeStatus", method = RequestMethod.POST)
    public String changeStatus(@RequestBody String jsonStr) {
        logger.info("changeStatus category：{}", jsonStr);
        try
        {
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            Assert.notNull(modify, "未登录无法操作");
            CategoryStatusDTO categoryDTO = getFormJSON(jsonStr, CategoryStatusDTO.class);
            Assert.hasLength(categoryDTO.getId(), "类目ID 不能为空");
            categoryService.changeStatus(categoryDTO.getId(), categoryDTO.getStatus(), modify);
            return getResultJSONStr(SUCCESS_BOOL, CHANGE_STATUS_SUCCESS, SUCCESS_STATUS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("ChangeStatus InformationCategoryModel error，input error", e);
            throw new InformationException(InformationConstants.FIELD_ILLEGAL, e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("ChangeStatus InformationCategoryModel error，input error", e);
            throw new ApecRuntimeException("禁用失败，请重试");
        }
    }

    /**
     * 根据 类目编号 获取类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/queryByMerchantId", method = RequestMethod.POST)
    public String queryByMerchantId(@RequestBody String jsonStr) {
        logger.info("Query category：{}", jsonStr);
        CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
        List<CategoryVO> result = categoryService.queryByMerchantId(categoryDTO.getMerchantId());
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }
}