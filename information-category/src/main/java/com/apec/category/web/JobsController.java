package com.apec.category.web;

import com.apec.category.service.CategoryService;
import com.apec.framework.base.BaseController;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.information.base.MyController;
import com.apec.information.constant.InformationConstants;
import com.apec.information.dto.CategoryDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.vo.CategoryVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @description 分类的接口
 * @date 2018/7/18 20:09
 **/
@RestController
@RequestMapping("/jobs")
public class JobsController extends MyController
{
    private static final Logger logger = LoggerFactory.getLogger(JobsController.class);

    @Autowired
    private CategoryService categoryService;

    /**
     * 新增类目
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/pickOrCreate", method = RequestMethod.POST)
    public String pickOrCreate(@RequestBody String jsonStr)
    {
        logger.info("pick or create category：{}", jsonStr);
        CategoryDTO categoryDTO = getFormJSON(jsonStr, CategoryDTO.class);
        CategoryVO result = categoryService.pickOrCreate(categoryDTO);
        return success(result);
    }
}