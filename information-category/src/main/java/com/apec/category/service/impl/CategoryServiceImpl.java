package com.apec.category.service.impl;

import com.apec.category.dao.CategoryDAO;
import com.apec.category.model.InformationCategoryModel;
import com.apec.category.model.QInformationCategoryModel;
import com.apec.category.service.CategoryService;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.information.dto.CategoryDTO;
import com.apec.information.enumtype.YorNEnum;
import com.apec.information.handler.InformationException;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.CategoryVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.QueryFactory;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @date 2018/7/18 16:05
 **/
@Service
public class CategoryServiceImpl implements CategoryService
{
    @Override
    public List<CategoryVO> query(String orgNo)
    {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        if(!ROOT_NODE.equals(orgNo))
        {
            predicate = predicate.and(qCategory.orgNo.eq(orgNo));
        }
        predicate = predicate.and(qCategory.visiableMana.eq(YorNEnum.Y.getCode()));
        Sort sort = new Sort(Sort.Direction.DESC, "sort");
        List<InformationCategoryModel> list = (List<InformationCategoryModel>)categoryDAO.findAll(predicate, sort);
        return toResult(list);
    }

    @Override
    public void update(CategoryDTO categoryDTO, String modify)
    {
        InformationCategoryModel category = categoryDAO.findOne(categoryDTO.getId());
        if(null == category)
        {
            throw new InformationException("25012002");
        }
        BeanUtils.copyPropertiesIgnoreNullFilds(categoryDTO, category, EGNORE_FIELDS);
        categoryDAO.saveAndFlush(category);
    }

    @Override
    public void delete(String id, String modify)
    {
        try
        {
            InformationCategoryModel category = categoryDAO.findOne(id);
            category.setEnableFlag(EnableFlag.N);
            category.setLastUpdateBy(modify);
            category.setLastUpdateDate(new Date());
            categoryDAO.save(category);
        }
        catch (Exception e)
        {
            logger.error("Delete error！", id, e);
            throw new InformationException("25012001");
        }
    }

    @Override
    public void changeStatus(String id, String status, String modify)
    {
        try
        {
            InformationCategoryModel category = categoryDAO.findOne(id);
            category.setEnableFlag(EnableFlag.Y);
            category.setStatus(status);
            category.setLastUpdateBy(modify);
            category.setLastUpdateDate(new Date());
            categoryDAO.save(category);
        }
        catch (Exception e)
        {
            logger.error("Delete error！", id, e);
            throw new InformationException("25012001");
        }
    }

    @Override
    public void insert(CategoryDTO categoryDTO, String modify)
    {
        BooleanExpression predicate = QInformationCategoryModel.informationCategoryModel.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(QInformationCategoryModel.informationCategoryModel.parentNo.eq(categoryDTO.getParentNo()));
        predicate = predicate.and(QInformationCategoryModel.informationCategoryModel.categoryName.eq(categoryDTO.getCategoryName()));
        long count = categoryDAO.count(predicate);
        if (count > 0) {
            logger.error("Delete error！");
            throw new InformationException("25016022","是否重复新增");
        }
        InformationCategoryModel category = createCategory(categoryDTO, modify);
        InformationCategoryModel parent = findByNo(categoryDTO.getParentNo());
        category.setOrgNo(parent.getOrgNo());
        category.setOrgName(parent.getOrgName());
        Long childNumber = findByParentNo(categoryDTO.getParentNo()) + 1;
        category.setCategoryNo(parent.getCategoryNo() + CONNECT + childNumber);
        category.setPath(parent.getPath() + LINE + childNumber);
        categoryDAO.save(category);
    }

    @Override
    public CategoryVO pickOrCreate(CategoryDTO categoryDTO)
    {
        if (categoryDTO.getExternalNo().equals("1017901")) {
            logger.info(">>>>>>>>>>>>{}",categoryDTO);
        }
        BooleanExpression predicate = QInformationCategoryModel.informationCategoryModel.externalNo
            .eq(categoryDTO.getExternalNo());
        predicate = predicate.and(QInformationCategoryModel.informationCategoryModel.orgNo.eq(categoryDTO.getOrgNo()));
        InformationCategoryModel category = categoryDAO.findOne(predicate);
        if(null == category)
        {
            category = createCategory(categoryDTO, categoryDTO.getOrgNo());
            InformationCategoryModel parent = findByNo(categoryDTO.getParentNo());
            category.setOrgNo(categoryDTO.getOrgNo());
            category.setOrgName(parent.getOrgName());
            category.setExternalNo(categoryDTO.getExternalNo());
            Long childNumber = findByParentNo(categoryDTO.getParentNo()) + 1;
            category.setCategoryNo(parent.getCategoryNo() + CONNECT + childNumber);
            category.setPath(parent.getPath() + LINE + childNumber);
            categoryDAO.save(category);
        }
        CategoryVO result = new CategoryVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(category, result);
        return result;
    }

    @Override
    public List<CategoryVO> queryByCategoryNo(String categoryNo, String merchantId) {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);

        if(StringUtils.isNotBlank(merchantId)){
            if("12301".equals(merchantId)){
                merchantId="12101";
                predicate = predicate.and(qCategory.parentNo.eq(categoryNo));
            }
            if(!"DF".equals(merchantId)){
                predicate = predicate.and(qCategory.status.eq(DEFAULT_STATUS));
                predicate = predicate.and(qCategory.merchantId.in(merchantId, "DF"));
            }
            predicate = predicate.and(qCategory.orgNo.eq("DF"));
            predicate = predicate.and(qCategory.parentNo.eq(categoryNo));
        }else{
            predicate = predicate.and(qCategory.categoryNo.like("%"+categoryNo+"%"));
        }
        List<InformationCategoryModel> list = (List<InformationCategoryModel>)categoryDAO.findAll(predicate);
        List<CategoryVO> categoryVOS = new ArrayList<>();
        list.forEach(informationCategoryModel -> {
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(informationCategoryModel, categoryVO);
            categoryVOS.add(categoryVO);
        });
        if("DF".equals(merchantId)){
            BooleanExpression predicate2 = qCategory.enableFlag.eq(EnableFlag.Y);
            predicate2 = predicate2.and(qCategory.categoryNo.eq("znw_1_5"));
            predicate2 = predicate2.and(qCategory.orgNo.eq("znw"));
            InformationCategoryModel one = categoryDAO.findOne(predicate2);
            if(Objects.nonNull(one)){
                CategoryVO categoryVO = new CategoryVO();
                BeanUtils.copyPropertiesIgnoreNullFilds(one, categoryVO);
                categoryVOS.add(categoryVO);
            }
        }
        return categoryVOS;
    }

    /**
     * @param merchantId
     * @return
     */
    @Override
    public List<CategoryVO> queryByMerchantId(String merchantId) {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        predicate = qCategory.status.eq(DEFAULT_STATUS);

        List<InformationCategoryModel> list = (List<InformationCategoryModel>)categoryDAO.findAll(predicate);
        List<CategoryVO> categoryVos = toResult(list);
        removeUnusedCategory(merchantId, categoryVos);
        return categoryVos;
    }

    private void removeUnusedCategory(String merchantId, List<CategoryVO> categoryVos) {
        for (int i = categoryVos.size() - 1 ; i >= 0; i-- ){
            CategoryVO categoryVO = categoryVos.get(i);
            if (!CollectionUtils.isEmpty(categoryVO.getChildList())) {
                removeUnusedCategory(merchantId, categoryVO.getChildList());
            }
            if(CollectionUtils.isEmpty(categoryVO.getChildList())
                    && !categoryVO.getMerchantIds().contains(merchantId)){
                categoryVos.remove(i);
            }
        };
    }
    // ----------------------------------- 私有方法与变量 ----------------------------------//

    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    private static final String[] EGNORE_FIELDS = ArrayUtils.toArray("id", "orgNo", "categoryNo", "parentNo");

    private QInformationCategoryModel qCategory = QInformationCategoryModel.informationCategoryModel;

    @Autowired
    private CategoryDAO categoryDAO;

    @Autowired
    private SnowFlakeKeyGen idGen;

    private List<CategoryVO> toResult(List<InformationCategoryModel> list)
    {
        Map<String, List<CategoryVO>> map = Maps.newHashMap();
        for(InformationCategoryModel category : list)
        {

            List<CategoryVO> childList = map.get(category.getParentNo());
            if(null == childList)
            {
                childList = Lists.newArrayList();
            }
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(category, categoryVO);
            categoryVO.setMerchantIds(
                    StringUtils.isNotBlank(category.getMerchantId())
                            ? Arrays.asList(category.getMerchantId().split(","))
                            : new ArrayList<>());
            childList.add(categoryVO);
            map.put(category.getParentNo(), childList);
        }
        return node(map, BEGIN_CATEGORY_NODE);
    }

    private List<CategoryVO> node(Map<String, List<CategoryVO>> map, String categoryNo)
    {
        List<CategoryVO> result = map.get(categoryNo);
        if(null == result)
        {
            return null;
        }
        result.forEach(categoryVO -> categoryVO.setChildList(node(map, categoryVO.getCategoryNo())));
        return result;
    }

    private InformationCategoryModel createCategory(CategoryDTO categoryDTO, String modify)
    {
        InformationCategoryModel category = new InformationCategoryModel();
        Date now = new Date();
        category.setId(Long.toString(idGen.nextId()));
        category.setCreateBy(modify);
        category.setLastUpdateBy(modify);
        category.setCreateDate(now);
        category.setLastUpdateDate(now);
        category.setCategoryName(categoryDTO.getCategoryName());
        category.setParentNo(categoryDTO.getParentNo());
        category.setSort(categoryDTO.getSort());
        category.setModule(categoryDTO.getModule());
        category.setDef(categoryDTO.getDef());
        category.setVisiableApp(categoryDTO.getVisiableApp());
        category.setVisiableMana(categoryDTO.getVisiableMana());
        category.setMerchantId(categoryDTO.getMerchantId());
        return category;
    }

    private InformationCategoryModel findByNo(String categoryNo)
    {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qCategory.categoryNo.eq(categoryNo));
        return categoryDAO.findOne(predicate);
    }

    private Long findByParentNo(String parentNo)
    {
//        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
//        predicate = predicate.and(qCategory.parentNo.eq(parentNo));
        /**
         * 查询所有，避免软删除导致category + 1重复
         */
        BooleanExpression predicate = qCategory.parentNo.eq(parentNo);
        return categoryDAO.count(predicate);
    }

}