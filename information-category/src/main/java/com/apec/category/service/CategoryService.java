package com.apec.category.service;

import com.apec.information.dto.CategoryDTO;
import com.apec.information.vo.CategoryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 14:05
 **/

public interface CategoryService
{
    /**
     * @param orgNo
     * @return
     */
    List<CategoryVO> query(String orgNo);

    /**
     * 更新操作
     * @param categoryDTO
     */
    void update(CategoryDTO categoryDTO, String modify);

    /**
     * 根据ID进行删除
     * @param id
     */
    void delete(String id, String modify);
    void changeStatus(String id, String status, String modify);

    /**
     * 增加子分类
     * @param categoryDTO
     */
    void insert(CategoryDTO categoryDTO, String modify);

    /**
     * 选取或创建
     * @param categoryDTO
     * @return
     */
    CategoryVO pickOrCreate(CategoryDTO categoryDTO);

    /**
     * @param categoryNo
     * @return
     */
    List<CategoryVO> queryByCategoryNo(String categoryNo, String merchantId);

    /**
     *
     * @param merchantId
     * @return
     */
    List<CategoryVO> queryByMerchantId(String merchantId);
}