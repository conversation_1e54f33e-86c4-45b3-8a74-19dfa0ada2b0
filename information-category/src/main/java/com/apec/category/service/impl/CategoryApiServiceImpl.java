package com.apec.category.service.impl;

import com.apec.category.dao.CategoryDAO;
import com.apec.category.model.InformationCategoryModel;
import com.apec.category.model.QInformationCategoryModel;
import com.apec.category.service.CategoryApiService;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.information.enumtype.YorNEnum;
import com.apec.information.vo.CategoryVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import static com.apec.information.constant.InformationConstants.BEGIN_CATEGORY_NODE;

/**
 *
 * <AUTHOR>
 * @date 2019/4/15 15:25
 */
@Service
public class CategoryApiServiceImpl implements CategoryApiService {

    private static final Logger logger = LoggerFactory.getLogger(CategoryServiceImpl.class);

    private QInformationCategoryModel qCategory = QInformationCategoryModel.informationCategoryModel;

    @Autowired
    private CategoryDAO categoryDAO;

    @Override
    public List<CategoryVO> query(String orgNo) {
        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qCategory.visiableMana.eq(YorNEnum.Y.getCode()));
        predicate = predicate.and(qCategory.visiableApp.eq(YorNEnum.Y.getCode()));
        predicate = predicate.and(qCategory.parentNo.ne(BEGIN_CATEGORY_NODE));
        predicate = predicate.and(qCategory.orgNo.eq(orgNo));
        Sort sort = new Sort(Sort.Direction.ASC, "sort");
        List<CategoryVO> result = new ArrayList<>();
        List<InformationCategoryModel> list = (List<InformationCategoryModel>)categoryDAO.findAll(predicate, sort);
        List<CategoryVO> childList = new ArrayList<>();
        list.forEach(informationCategoryModel -> {
            CategoryVO vo = new CategoryVO();
            BeanUtils.copyProperties(informationCategoryModel, vo);
            String categoryNo = informationCategoryModel.getCategoryNo();
            String[] categoryNos = categoryNo.split("_");
            vo.setNodeLevel(YorNEnum.N.getCode());
            //节点是否二级
            if ((categoryNos.length-1) == 2) {
                vo.setNodeLevel(YorNEnum.Y.getCode());
            }
            childList.add(vo);
        });
        childList.sort(Comparator.comparing(CategoryVO::getSort).reversed());
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setChildList(childList);
        result.add(categoryVO);
        return result;
    }

//    @Override
//    public List<CategoryVO> query(String orgNo)
//    {
//        BooleanExpression predicate = qCategory.enableFlag.eq(EnableFlag.Y);
//        if(!ROOT_NODE.equals(orgNo))
//        {
//            predicate = predicate.and(qCategory.orgNo.eq(orgNo));
//        }
//        predicate = predicate.and(qCategory.visiableMana.eq(YorNEnum.Y.getCode()));
//        predicate = predicate.and(qCategory.visiableApp.eq(YorNEnum.Y.getCode()));
//        Sort sort = new Sort(Sort.Direction.DESC, "sort");
//        List<InformationCategoryModel> list = (List<InformationCategoryModel>)categoryDAO.findAll(predicate, sort);
//        return toResult(list);
//    }

    private List<CategoryVO> toResult(List<InformationCategoryModel> list)
    {
        Map<String, List<CategoryVO>> map = Maps.newHashMap();
        for(InformationCategoryModel category : list)
        {
            List<CategoryVO> childList = map.get(category.getParentNo());
            if(null == childList)
            {
                childList = Lists.newArrayList();
            }
            CategoryVO categoryVO = new CategoryVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(category, categoryVO);
            childList.add(categoryVO);
            map.put(category.getParentNo(), childList);
        }
        return node(map, BEGIN_CATEGORY_NODE);
    }

    private List<CategoryVO> node(Map<String, List<CategoryVO>> map, String categoryNo)
    {
        List<CategoryVO> result = map.get(categoryNo);
        if(null == result)
        {
            return null;
        }
        result.forEach(categoryVO -> categoryVO.setChildList(node(map, categoryVO.getCategoryNo())));
        return result;
    }
}
