<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.apec</groupId>
        <artifactId>information-parent</artifactId>
        <version>1.0.1-RELEASE</version>
    </parent>

    <artifactId>information-category</artifactId>
    <name>information-category</name>

    <dependencies>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>information-core</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>framework-jpa</artifactId>
            <version>1.5.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>framework-springcloud</artifactId>
            <version>1.5.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>framework-common</artifactId>
            <version>1.5.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.apec</groupId>
            <artifactId>framework-cache</artifactId>
            <version>1.0.2-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-eureka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.10</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-sts</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>3.5.0</version>
        </dependency>

    </dependencies>

    <!-- 打包方式 -->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <!-- spring-boot:run 中文乱码解决 -->
                    <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- querydsl 插件 自动生成Qmodle，简化Criteria操作 -->
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.mysema.querydsl</groupId>
                        <artifactId>querydsl-apt</artifactId>
                        <version>3.7.3</version>
                    </dependency>
                </dependencies>

            </plugin>
        </plugins>
    </build>
</project>