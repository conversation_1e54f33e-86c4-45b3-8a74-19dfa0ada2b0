/*
 *
 *   @copyright      Copyright © 2017 - 2018. [深圳市中农易讯信息技术有限公司] All rights reserved.
 *   @project        APEC_CJ212_pages
 *   <AUTHOR>
 *   @date           2017-09-16 14:04:57
 *
 *   @lastModifie  2017-09-16 14:04:57
 *
 */

package com.apec;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.apec.framework.base.BaseApplication;
import com.apec.framework.springcloud.SpringCloudConfig;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/7/31 11:39
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableScheduling
@Import(value = SpringCloudConfig.class)
public class JobsApplication extends BaseApplication {
    public static void main(String[] args) {
        new SpringApplicationBuilder(JobsApplication.class).web(true).run(args);
    }
}