package com.apec.job.dao;

import com.apec.framework.jpa.dao.BaseDAO;
import com.apec.job.model.MtMarketZs;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * MtMarketZsDAO
 * <AUTHOR>
 * @date 17:17 17:18
 **/
@Repository
public interface MtMarketZsDAO extends BaseDAO<MtMarketZs, String>,
        CrudRepository<MtMarketZs, String>,
        JpaSpecificationExecutor<MtMarketZs> {

    @Query(value = "select * from (SELECT * FROM MARKET_ZS ORDER BY INFODATE DESC) where rownum=1", nativeQuery = true)
    MtMarketZs queryTopOne();

}

