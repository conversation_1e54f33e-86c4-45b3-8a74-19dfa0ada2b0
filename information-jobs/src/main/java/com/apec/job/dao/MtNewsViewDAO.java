package com.apec.job.dao;

import java.util.List;

import com.apec.job.model.MtNewsView;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.apec.framework.jpa.dao.BaseDAO;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/7/31 19:00
 * <AUTHOR>
 */
@Repository
public interface MtNewsViewDAO extends BaseDAO<MtNewsView, String>,
    CrudRepository<MtNewsView, String>,
    JpaSpecificationExecutor<MtNewsView>
{
    public MtNewsView findByTid(String tid);

    List<MtNewsView> findByReleaseTimeBetween(String start,String end);
}
