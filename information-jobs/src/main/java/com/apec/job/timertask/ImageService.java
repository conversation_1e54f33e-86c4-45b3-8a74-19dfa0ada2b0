package com.apec.job.timertask;

import com.aliyun.oss.OSSClient;
import com.apec.information.util.SnowFlakeKeyGen;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.net.URL;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/8/3 16:41
 * <AUTHOR>
 */
@Service
public class ImageService
{
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private SnowFlakeKeyGen keyGen;

    @Value("${aliyun.watermark}")
    private String watermark;

    String uploadAliyun(String source) throws Exception
    {
        if(StringUtils.isBlank(source))
        {
            return source;
        }
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        // 上传网络流。
        InputStream inputStream = new URL(source).openStream();
        String suffix = getSuffix(source);
        String objectName = keyGen.nextId() + "_" + suffix;
        ossClient.putObject(bucketName, objectName, inputStream);
        inputStream.close();
        ossClient.shutdown();
        return finalPerfix + objectName + watermark;
    }

    List<String> uploadAliyun(List<String> sourceList) throws Exception
    {
        if(CollectionUtils.isEmpty(sourceList))
        {
            return sourceList;
        }
        List<String> result = Lists.newArrayListWithCapacity(sourceList.size());
        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for(String source : sourceList)
        {
            // 上传网络流。
            InputStream inputStream = new URL(source).openStream();
            String suffix = getSuffix(source);
            String objectName = keyGen.nextId() + "_" + suffix;
            ossClient.putObject(bucketName, objectName, inputStream);
            result.add(finalPerfix + objectName + watermark);
            inputStream.close();
        }
        LOG.info("上传图片数量：{}", sourceList.size());
        // 关闭OSSClient。
        ossClient.shutdown();
        return result;
    }

    private static String getSuffix(String source)
    {
        String suffixes = "jpeg|gif|jpg|png|pdf|rar|zip";
        Pattern pat = Pattern.compile("[\\w]+[\\.](" + suffixes + ")");
        //正则判断
        Matcher mc = pat.matcher(source);
        while (mc.find())
        {
            return mc.group();
        }
        return "jpg";
    }

    @Value("${aliyun.endpoint}")
    private String endpoint;

    @Value("${aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.bucketName}")
    private String bucketName;

    @Value("${aliyun.finalPerfix}")
    private String finalPerfix;
}