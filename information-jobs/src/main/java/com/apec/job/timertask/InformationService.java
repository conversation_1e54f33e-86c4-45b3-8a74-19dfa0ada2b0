package com.apec.job.timertask;

import com.apec.job.model.InformationModel;
import com.apec.information.util.SnowFlakeKeyGen;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.lang.invoke.MethodHandles;
import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/8/3 16:41
 * <AUTHOR>
 */
@Service
public class InformationService
{
    /**
     * 日志句柄
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SnowFlakeKeyGen keyGen;

    void tbInformation(List<InformationModel> list)
    {
        for(InformationModel information : list)
        {
            Query query = Query.query(Criteria.where("informationNo").is(information.getInformationNo()));
            query.addCriteria(Criteria.where("orgNo").is(information.getOrgNo()));
            query.addCriteria(Criteria.where("categoryNo").is(information.getCategoryNo()));
            InformationModel info = mongoTemplate.findOne(query, InformationModel.class);
            if(null != info)
            {
                information.setId(info.getId());
                information.setLikeList(info.getLikeList());
                information.setCollectList(info.getCollectList());
            }
            else
            {
                information.setId(Long.toString(keyGen.nextId()));
            }
            mongoTemplate.save(information);
            LOG.debug("//// 保存成功：{}", information.getId());
        }
    }
}
