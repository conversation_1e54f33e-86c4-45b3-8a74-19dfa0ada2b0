package com.apec.job.timertask;

import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.springcloud.SpringCloudClient;
import com.apec.information.dto.CategoryDTO;
import com.apec.information.dto.ImageInfoDTO;
import com.apec.information.enumtype.LinkTypeEnum;
import com.apec.information.util.ArrayDateUtil;
import com.apec.information.vo.CategoryVO;
import com.apec.job.dao.MtNewsViewDAO;
import com.apec.job.model.InformationModel;
import com.apec.job.model.MtNewsView;
import com.apec.job.model.QMtNewsView;
import com.google.common.collect.Lists;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.invoke.MethodHandles;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.apec.information.constant.InformationConstants.DEFAULT_STATUS;

/**
 * 同步沐甜资讯定时任务
 * @date 2018/10/15
 * <AUTHOR>
 */
@Component
public class MtQuartzService
{
    /**
     * 每两个小时处理一次定时任务
     */
    @Scheduled(cron = "0 45 8-22/1 * * ?")
//                @Scheduled(cron = "0/5 * * * * ?")
    public void timerToNow()
    {
        Long timestamp = System.currentTimeMillis();
        Date endDate = new Date(timestamp);
        Date beginDate = ArrayDateUtil.nextDate(endDate, -30);
        LOG.debug("//// 本次将同步{} 到 {} 的资讯数据", beginDate, endDate);
        List<MtNewsView> list = mtNewsViewDAO
            .findByReleaseTimeBetween(ArrayDateUtil.ymdhmsToString(beginDate),
                                      ArrayDateUtil.ymdhmsToString(endDate));
//        BooleanExpression predicate = QMtNewsView.mtNewsView.title.eq("5月份，纽约原糖期货小幅上涨");
//        List<MtNewsView> list = (List<MtNewsView>) mtNewsViewDAO.findAll(predicate);
        List<InformationModel> infoList = Lists.newArrayList();
        Date now = new Date();

        for(MtNewsView mtNewsView : list)
        {
            InformationModel info = new InformationModel();
            info.setOrgNo(btOrgNo);
            info.setInformationIssuer(mtNewsView.getAuthor());
            info.setInformationSummary(mtNewsView.getInfoSummery());
            info.setInformationTitle(mtNewsView.getTitle());
            info.setInformationNo(mtNewsView.getTid());
            info.setInformationChildLink(mtNewsView.getHtmlUrl());
            try
            {
                if(StringUtils.isNotBlank(mtNewsView.getImageUrl()))
                {
                    List<String> sourceList = Lists.newArrayList(MT_URL + mtNewsView.getImageUrl());
                    ImageInfoDTO imageDTO = new ImageInfoDTO();
                    imageDTO.setSourceList(sourceList);
                    List<String> aliyunList = scc.restfulPostList(checkAndUploadPicture,
                                                                  JsonUtils.toJSONString(imageDTO),
                                                                  String.class);
                    String coverImg = aliyunList.get(ZERO) + watermark;
                    info.setInformationCoverImage(coverImg);
                }
                toAliyun(info, mtNewsView.getText());
            }
            catch (Exception e)
            {
                LOG.error("上传阿里云失败", e);
                return;
            }
            initCategoryAndLabel(info, mtNewsView.getBid(), mtNewsView.getColumnb(), mtNewsView.getColumnId(), mtNewsView.getColumnName());

            info.setInformationSource(LinkTypeEnum.OUTER.val());
            info.setInformationSourceLink(MT_URL);
            info.setInformationSourceName(mtNewsView.getSourceId());
            info.setInformationModule(1);
            info.setInformationPublicStatus(DEFAULT_STATUS);
            info.setInformationEditable(false);

            info.setCreateBy(mtNewsView.getAuthor());
            info.setLastUpdateBy(mtNewsView.getAuthor());
            Date mtCreateDate = ArrayDateUtil.ymdhmsToDate(mtNewsView.getReleaseTime());
            info.setCreateDate(mtCreateDate);
            info.setLastUpdateDate(mtCreateDate);
            info.setLastInteractTimestamp(timestamp);
            info.setMarketId("000");
            info.setMerchantId("000");
            info.setMerchantName("白糖");
            infoList.add(info);
        }
        LOG.debug("//// 本次将同步{}条资讯", infoList.size());
        informationService.tbInformation(infoList);
    }

    /**
     * 日志句柄
     */
    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    private static final int ZERO = 0;

    private String btOrgNo = "000";

    private String PARENT_NO = "000_1";

    private static final String MATCH_STR = "<img(.*?)src=\"(.*?)\"(.*?)/>";

    private static final String MT_URL = "http://www.msweet.com.cn";

    private static final String START_WITH = "/";

    private static final Pattern PATTERN = Pattern.compile(MATCH_STR);

    @Autowired
    private MtNewsViewDAO mtNewsViewDAO;

    @Autowired
    private InformationService informationService;

    @Autowired
    private SpringCloudClient scc;

    @Value("${mt.jobs.timer.day.number}")
    private Integer dayNum;

    @Value("${info.pickOrCreate.category}")
    private String pickOrCreateCategory;

    @Value("${info.insert.label}")
    private String insertLabel;

    @Value("${info.picture.checkAndUpload}")
    private String checkAndUploadPicture;

    @Value("${aliyun.watermark}")
    private String watermark;

    private void initCategoryAndLabel(InformationModel info, String bid, String columnb, String id, String columnName)
    {
        if(StringUtils.isBlank(bid) && StringUtils.isBlank(id))
        {
            return;
        }
        if(StringUtils.isBlank(bid))
        {
            bid = id;
            columnb = columnName;
        }
        // 获取二级类目
        CategoryVO categoryVO = pickOrCreateCategory(PARENT_NO, bid, columnb);
        if(bid.equals(id))
        {
            info.setCategoryNo(categoryVO.getCategoryNo());
            info.setCategoryNos(Lists.newArrayList(categoryVO.getCategoryNo()));
            info.setCategoryName(categoryVO.getCategoryName());
            return;
        }
        // 获取三级类目
        categoryVO = pickOrCreateCategory(categoryVO.getCategoryNo(), id, columnName);
        info.setCategoryNo(categoryVO.getCategoryNo());
        info.setCategoryNos(Lists.newArrayList(categoryVO.getCategoryNo()));
        info.setCategoryName(categoryVO.getCategoryName());
    }

    private CategoryVO pickOrCreateCategory(String parentNo, String id, String columnName)
    {
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setOrgNo(btOrgNo);
        categoryDTO.setExternalNo(id);
        categoryDTO.setCategoryName(columnName);
        categoryDTO.setSort(1);
        categoryDTO.setModule(1);
        categoryDTO.setParentNo(parentNo);
        String jsonStr = scc.restfulPostStr(pickOrCreateCategory, JsonUtils.toJSONString(categoryDTO));
        return JsonUtils.parseObject(jsonStr, CategoryVO.class);
    }

    private void toAliyun(InformationModel info, String content) throws Exception
    {
        if(StringUtils.isBlank(content))
        {
            info.setInformationContent("");
            return;
        }
        Matcher matcher = PATTERN.matcher(content);
        List<String> oldList = Lists.newArrayList();
        List<String> imgList = Lists.newArrayList();
        while (matcher.find())
        {
            String url = matcher.group(2);
            if(url.startsWith(START_WITH))
            {
                oldList.add(url);
                imgList.add(MT_URL + url);
            }
        }
        if(!CollectionUtils.isEmpty(imgList))
        {
            ImageInfoDTO imageDTO = new ImageInfoDTO();
            imageDTO.setSourceList(imgList);
            List<String> newList = scc.restfulPostList(checkAndUploadPicture,
                                                       JsonUtils.toJSONString(imageDTO),
                                                       String.class);
            info.setInformationImageList(newList);
            for(int size = 0; size < oldList.size(); size++)
            {
                String oldImg = oldList.get(size);
                String newImg = newList.get(size);
                content = content.replace(oldImg, newImg);
            }
        }
        info.setInformationContent(content);
    }
}