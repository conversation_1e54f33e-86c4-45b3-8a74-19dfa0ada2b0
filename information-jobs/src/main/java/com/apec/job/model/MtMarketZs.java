package com.apec.job.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/7/31 17:54
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "MARKET_ZS")
public class MtMarketZs
{
    @Id
    @Column(name = "ID")
    private String ID;

    @Column(name = "ZHJG")
    private String ZHJG;

    @Column(name = "ZHZD")
    private String ZHZD;

    @Column(name = "DFTJG")
    private String DFTJG;

    @Column(name = "DFTZD")
    private String DFTZD;

    @Column(name = "YTJG")
    private String YTJG;

    @Column(name = "YTZD")
    private String YTZD;

    @Column(name = "INFODATE")
    private String INFODATE;

    @Column(name = "XHJG")
    private String XHJG;

    @Column(name = "XHZD")
    private String XHZD;

}

