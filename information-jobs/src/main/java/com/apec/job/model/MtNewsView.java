package com.apec.job.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/7/31 17:54
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "NEWS_VIEWS")
public class MtNewsView
{
    @Column(name = "SITENAME")
    private String siteName;

    /**
     * 二级目录Id
     */
    @Column(name = "BID")
    private String bid;

    /**
     * 二级目录
     */
    @Column(name = "COLUMNB")
    private String columnb;

    @Column(name = "AID")
    private String aid;

    @Column(name = "COLUMNA")
    private String columna;

    /**
     * 三级目录Id
     */
    @Column(name = "COLUMNID")
    private String columnId;

    /**
     * 三级目录
     */
    @Column(name = "COLUMNNAME")
    private String columnName;

    /**
     * 文章编号
     */
    @Id
    @Column(name = "TID")
    private String tid;

    /**
     * 页面标题名称
     */
    @Column(name = "TITLE")
    private String title;

    /**
     * 创建时间
     */
    @Column(name = "RELEASETIME")
    private String releaseTime;

    /**
     * 发布源名称
     */
    @Column(name = "SOURCE_ID")
    private String sourceId;

    /**
     * 发布人
     */
    @Column(name = "AUTHOR")
    private String author;

    @Column(name = "TEXT")
    private String Text;

    /**
     * 摘要
     */
    @Column(name = "INFOSUMMARY")
    private String infoSummery;

    /**
     * 发布源
     */
    @Column(name = "IMAGEURL")
    private String imageUrl;

    /**
     * 发布源
     */
    @Column(name = "HTMLURL")
    private String htmlUrl;
}
