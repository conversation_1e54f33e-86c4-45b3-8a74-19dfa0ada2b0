package com.apec.job.controller;

import com.apec.framework.base.BaseController;
import com.apec.job.model.MtMarketZs;
import com.apec.job.service.MtMarketZsService;
import com.apec.job.timertask.MtQuartzService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.apec.information.constant.InformationConstants.SUCCESS_BOOL;
import static com.apec.information.constant.InformationConstants.SUCCESS_STATUS;

@RestController
@RequestMapping("mtMarketZs")
public class MtMarketZsController extends BaseController {

    @Autowired
    private MtMarketZsService mtMarketZsService;

    @Autowired
    private MtQuartzService mtQuartzService;

    @RequestMapping(value = "queryMtMarketZs", method = RequestMethod.POST, produces = "application/json")
    public String queryMtMarketZs() {
        MtMarketZs mtMarketZs = mtMarketZsService.queryMtMarketZs();
        return getResultJSONStr(SUCCESS_BOOL, mtMarketZs, SUCCESS_STATUS);
    }
    @RequestMapping(value = "execSync", method = RequestMethod.POST, produces = "application/json")
    public String execSync() {
        mtQuartzService.timerToNow();
        return getResultJSONStr(SUCCESS_BOOL, "", SUCCESS_STATUS);
    }
}
