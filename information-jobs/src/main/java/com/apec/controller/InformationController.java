package com.apec.controller;

import com.apec.information.dto.InformationDTO;
import com.apec.information.handler.InformationException;
import com.apec.job.timertask.MtQuartzService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.information.constant.InformationConstants.FIELD_ILLEGAL;
import static com.apec.information.constant.InformationConstants.UPDATE_SUCCESS;

/**
 * ClassName InformationController
 * Description InformationController
 *
 * <AUTHOR>
 * @date 2022/1/6 15:53
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping(value = "/information")
public class InformationController extends MyController {

    @Autowired
    private MtQuartzService mtQuartzService;

    /**
     * 手动执行定时任务
     *
     * @return
     */
    @RequestMapping(value = "/timerToNow", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public void timerToNow() {
        log.debug("timerToNow");
        mtQuartzService.timerToNow();
    }

}