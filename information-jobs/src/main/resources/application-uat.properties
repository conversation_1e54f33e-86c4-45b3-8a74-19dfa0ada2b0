workerId=1

#region eureka config
eureka.client.serviceUrl.defaultZone=http://*************:1111/eureka/
eureka.client.registry-fetch-interval-seconds=5
eureka.instance.lease-expiration-duration-in-seconds=15
eureka.instance.lease-renewal-interval-in-seconds=5
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
eureka.instance.prefer-ip-address=true
#endregion

#region log config
logging.level.com.apec=info
logging.level.org.hibernate=ERROR
logging.level.root=INFO
#endregion
#spring.datasource.primary.url=*****************************************************************************************************************
#spring.datasource.primary.username=root
#spring.datasource.primary.password=123456
#spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
#region mysql data config
#spring.jpa.properties.hibernate.dialect = org.hibernate.dialect.MySQL5InnoDBDialect
spring.datasource.primary.url=********************************************************************************************************************
spring.datasource.primary.password=Znw&231
spring.datasource.primary.username=znw
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
#endregion

#region rabbitmq config
spring.rabbitmq.host=***************
spring.rabbitmq.password=123456
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.virtual-host=/apec
#endregion



spring.redis.host=***************
spring.redis.port=7001
spring.redis.host2=***************
spring.redis.port2=7002
spring.redis.timeout=5000
spring.redis.maxRedirections=3000
spring.redis.password=admin@123

redis.database=6
redis.host=***************
redis.port=16379
redis.password=znw@123
redis.timeout=5000
redis.pool.max-active=8
redis.pool.max-wait=-1
redis.pool.max-idle=8
redis.pool.min-idle=0


ftp.host=************
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/bank_other/
ftp.url.fix=http://seedtest.ap-ec.cn
#mongo
spring.data.mongodb.uri=mongodb://***************:27017/information
