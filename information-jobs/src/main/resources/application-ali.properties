logging.level.com.apec=DEBUG
workerId=1


eureka.client.serviceUrl.defaultZone=http://eureka.aliyun.magpie.com:1111/eureka/

#mongodb uri
spring.data.mongodb.uri=mongodb://mongo.aliyun.magpie.com:27017/information

spring.datasource.primary.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.primary.url=*****************************************
spring.datasource.primary.username=viewsdb
spring.datasource.primary.password=viewsdb
spring.datasource.primary.initialSize=3
spring.datasource.primary.maxActive=20
spring.datasource.primary.maxAge=120000
spring.datasource.primary.validationQuery=select 6

redis.database=0
redis.host=redis.aliyun.magpie.com
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

ftp.host=ftp.aliyun.magpie.com
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/

spring.redis.host=redis.aliyun.magpie.com
spring.redis.port=7000
spring.redis.host2=redis.aliyun.magpie.com
spring.redis.port2=7001
spring.redis.timeout=5000
spring.redis.maxRedirections=5

spring.rabbitmq.host=rabbitmq.aliyun.magpie.com
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec
