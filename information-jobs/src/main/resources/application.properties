spring.profiles.active=test
server.port=25017


aliyun.endpoint=https://oss-cn-shenzhen.aliyuncs.com
aliyun.accessKeyId=LTAI7nCQWrPm6iH1
aliyun.accessKeySecret=oLnfIOTt5vu06ZgCPXsRd4DIkPvrJ2
aliyun.bucketName=magpie-pic
aliyun.finalPerfix=https://magpie-pic.oss-cn-shenzhen.aliyuncs.com/
aliyun.watermark=?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,size_30,image_N2M1YzBmYmQ2NjI0Y2UzOTE4NTFiZDEyZjkzYWNiMTQucG5nP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLFBfMjA=,t_70,g_se,x_10,y_10

spring.jpa.properties.hibernate.show_sql=true

spring.datasource.primary.time-between-eviction-runs-millis=20000
spring.datasource.primary.min-evictable-idle-time-millis=30000
spring.datasource.primary.test-while-idle=true
spring.datasource.primary.test-on-borrow=false

spring.application.name=INFORMATION-JOBS-SERVICE
eureka.instance.prefer-ip-address=true

workerId=7

spring.messages.basename=errormessage

eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}
eureka.client.serviceUrl.defaultZone=http://${eureka.instance.hostname}/eureka/1

mt.zxtype=
mt.jobs.timer.day.number=-3

info.pickOrCreate.category=http://INFORMATION-CATEGORY-SERVICE/jobs/pickOrCreate
info.insert.label=http://INFORMATION-SETTING-SERVICE/setting/insertLabel
info.picture.checkAndUpload=http://INFORMATION-PICTURE-SERVICE/jobs/checkAndUpload