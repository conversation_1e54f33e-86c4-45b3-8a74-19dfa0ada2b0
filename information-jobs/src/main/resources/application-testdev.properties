server.port=25017
logging.level.com.apec=DEBUG

#mongodb uri
spring.data.mongodb.uri=mongodb://192.168.7.201:27017/information

eureka.client.serviceUrl.defaultZone=http://*************:1111/eureka/

spring.datasource.primary.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.primary.url=*******************************************
spring.datasource.primary.username=viewsdb
spring.datasource.primary.password=viewsdb
spring.datasource.primary.initialSize=3
spring.datasource.primary.maxActive=20
spring.datasource.primary.maxAge=120000
spring.datasource.primary.validationQuery=select 6

redis.database=8
redis.host=*************
redis.port=9001
redis.password=foobared
redis.timeout=5000
redis.pool.max-active=8
redis.pool.max-wait=-1
redis.pool.max-idle=8
redis.pool.min-idle=0

#
spring.redis.host=*************
spring.redis.port=9001
spring.redis.host2=*************
spring.redis.port2=9002
spring.redis.timeout=5000
spring.redis.maxRedirections=3000
spring.redis.password=foobared

#
spring.rabbitmq.host=*************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec
