server.port=23212
logging.level.com.apec=DEBUG

#mongodb uri
spring.data.mongodb.uri=mongodb://10.100.100.63:27017/cms

eureka.client.serviceUrl.defaultZone=http://**************:1111/eureka/

spring.datasource.primary.driver-class-name=oracle.jdbc.driver.OracleDriver
spring.datasource.primary.url=*******************************************
spring.datasource.primary.username=viewsdb
spring.datasource.primary.password=viewsdb
spring.datasource.primary.initialSize=3
spring.datasource.primary.maxActive=20
spring.datasource.primary.maxAge=120000
spring.datasource.primary.validationQuery=select 6

redis.database=8
redis.host=**************
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=
spring.redis.port=7000
spring.redis.host2=
spring.redis.port2=7001
spring.redis.nodes=
spring.redis.timeout=5000
spring.redis.maxIdle=8
spring.redis.minIdle=4
spring.redis.maxRedirections=3000

spring.rabbitmq.host=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/dztest