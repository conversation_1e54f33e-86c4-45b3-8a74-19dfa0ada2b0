package com.apec.information.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资讯记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Data
public class InformationVO {
    /**
     * 主键
     */
    private String id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 最后更新时间
     */
    private Date lastUpdateDate;

    /**
     * 组织编码---市场编码
     */
    private String orgNo;
    private String categoryNo;
    /**
     * 资讯编号
     */
    private String informationNo;

    /**
     * 模板（1:文章、2:快讯）
     */
    private Integer informationModule;

    /**
     * 发布源
     */
    private String informationSource;

    /**
     * 发布源名称
     */
    private String informationSourceName;

    /**
     * 发布源链接
     */
    private String informationSourceLink;

    /**
     * 发布源详情链接
     */
    private String informationChildLink;

    /**
     * 编辑状态
     */
    private Boolean informationEditable;

    /**
     * 发布状态
     */
    private String informationPublicStatus;

    /**
     * 页面名称
     */
    private String informationName;

    /**
     * 页面标题名称
     */
    private String informationTitle;

    /**
     * 发布人
     */
    private String informationIssuer;

    /**
     * 摘要
     */
    private String informationSummary;

    /**
     * 内容
     */
    private String informationContent;

    /**
     * 缩略图
     */
    private String informationCoverImage;

    /**
     * 所有图列表
     */
    private List<String> informationImageList;

    /**
     * 标签列表
     */
    private List<String> informationLabel;

    /**
     * 备注
     */
    private String informationRemark;

    /**
     *
     */
    private Integer informationCommentCount;
    /**
     * 商家编号
     */
    private String merchantId;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     *
     */
    private List<String> categoryNos;
    /**
     * 品类名字
     */
    private String categoryName;
}