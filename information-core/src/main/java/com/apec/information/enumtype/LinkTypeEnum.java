package com.apec.information.enumtype;

/**
 * 类 编 号：
 * 类 名 称：
 * 内容摘要：
 * 创建日期：2018/6/7 14:09
 * <AUTHOR>
 */
public enum LinkTypeEnum
{
    /**
     * 内链
     */
    INNER("1"),
    /**
     * 外链
     */
    OUTER("2"),;

    private final String val;

    LinkTypeEnum(String val)
    {
        this.val = val;
    }

    public String val()
    {
        return val;
    }

    final static EnumHelper<LinkTypeEnum, String> helper = new EnumHelper<>(LinkTypeEnum.class,
                                                                            link -> link.val());

    public static LinkTypeEnum find(String ordinal)
    {
        return helper.find(ordinal);
    }
}