package com.apec.information.enumtype;

public enum ShowStatus
{
    /**
     * 待审核
     */
    DEFAULT("0"),
    /**
     * 通过
     */
    PUBLIC("1"),
    /**
     * 驳回
     */
    PRIVATE("2"),;

    private final String val;

    ShowStatus(String val)
    {
        this.val = val;
    }

    public String val()
    {
        return val;
    }

    final static EnumHelper<ShowStatus, String> helper = new EnumHelper<>(ShowStatus.class,
                                                                          link -> link.val());

    public static ShowStatus find(String ordinal)
    {
        return helper.find(ordinal);
    }
}
