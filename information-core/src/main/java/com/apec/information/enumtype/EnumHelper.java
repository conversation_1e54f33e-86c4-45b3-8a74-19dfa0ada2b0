package com.apec.information.enumtype;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * @date 2018-03-01 14:10
 * <AUTHOR>
 */
public class EnumHelper<T extends Enum<T>, K>
{
    protected Map<K, T> map = new HashMap<K, T>();

    public EnumHelper(Class<T> clazz, EnumKeyGetter<T, K> keyGetter)
    {
        try
        {
            for(T enumValue : EnumSet.allOf(clazz))
            {
                map.put(keyGetter.getKey(enumValue), enumValue);
            }
        }
        catch (Exception e)
        {
            logger.error("获取失败");
        }
    }

    public T find(K key)
    {
        return map.get(key);
    }

    public T find(K key, T defautValue)
    {
        T value = map.get(key);
        if(value == null)
        {
            value = defautValue;
        }
        return value;
    }
    
    private Logger logger = LoggerFactory.getLogger(getClass());
}