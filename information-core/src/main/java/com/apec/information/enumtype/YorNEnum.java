package com.apec.information.enumtype;

public enum  YorNEnum {

    /**
     * 是
     */
    Y(0,"是"),

    /**
     * 否
     */
    N(1,"否");

    private Integer code;

    private String dec;

    YorNEnum(Integer code, String dec){
        this.code = code;
        this.dec = dec;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDec() {
        return dec;
    }

    public void setDec(String dec) {
        this.dec = dec;
    }


    public static YorNEnum getByCode(Integer code){
        for(YorNEnum type:values()){
            if(type.getCode().equals(code)){
                return type;
            }
        }
        return null;
    }

    public static String getDecByCode(Integer code){

        for(YorNEnum type:values()){
            if(type.getCode().equals(code)){
                return type.getDec();
            }
        }
        return null;
    }
}
