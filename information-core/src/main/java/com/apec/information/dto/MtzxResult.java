package com.apec.information.dto;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.enumtype.LinkTypeEnum;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 类 编 号：
 * 类 名 称：沐甜资讯接口返回
 * 内容摘要：
 * 创建日期：2018/6/7 11:58
 * <AUTHOR>
 */
@Data
public class MtzxResult
{
    String ADAPTER;

    String STATE;

    String SIZE;

    List<DATA> DATAS;

    @lombok.Data
    private class DATA
    {
        /**
         * 资讯主键
         */
        String ZX_KEY;

        /**
         * 资讯标题
         */
        String TITLE;

        /**
         * 小分类
         */
        String TYPENAME;

        /**
         * 资讯内容简介
         */
        String CONTENT;

        /**
         * 发布时间戳
         */
        long TIME;

        /**
         * 资讯来源
         */
        String SOURCE;

        /**
         * 是否收藏，A：收藏，B：未收藏
         */
        String IS_SC;

        /**
         * 评论数量（通过审批的）
         */
        int COMM_NUM;

        String URL_TYPE;
    }

    public PageDTO<InformationDTO> toCmsRichTextDTOPageDTO(int number, int pageSize)
    {
        PageDTO<InformationDTO> pageDTO = new PageDTO<>();

        long totalElements = Long.valueOf(getSIZE());
        int totalPages = totalElements == 0 ? 1 :
            (int)Math.ceil((double)totalElements / (double)pageSize);

        pageDTO.setNumber(number + 1);
        pageDTO.setTotalElements(totalElements);
        pageDTO.setTotalPages(totalPages);

        List<InformationDTO> list = null;
        if(null != DATAS)
        {
            list = Lists.newArrayList();
            for(DATA data : DATAS)
            {
                if(null != data)
                {
                    InformationDTO informationDTO = new InformationDTO();
                    informationDTO.setId(data.getZX_KEY());
                    informationDTO.setInformationNo(data.getZX_KEY());
                    informationDTO.setInformationSource(LinkTypeEnum.OUTER.val());
                    informationDTO.setInformationTitle(data.getTITLE());
                    informationDTO.setInformationContent(data.getCONTENT());
                    informationDTO.setCategoryNo(data.getTYPENAME());
                    informationDTO.setInformationSourceName(data.getSOURCE());
                    informationDTO.setCreateDate(new Date(data.getTIME()));
                    informationDTO.setInformationEditable(false);
                    list.add(informationDTO);
                }
            }
        }
        pageDTO.setRows(list);
        return pageDTO;
    }
}