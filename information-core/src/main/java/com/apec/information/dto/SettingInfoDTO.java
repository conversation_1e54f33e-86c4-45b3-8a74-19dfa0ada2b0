package com.apec.information.dto;

import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * <AUTHOR>
 * @date 2018-09-17
 **/
@Getter
@Setter
@ToString
public class SettingInfoDTO extends BaseDTO
{
    private String id;

    private String orgNo;

    private String orgName;

    private String categoryId;

    private String settingKey;

    private String settingValue;

    private String settingType;

    private String settingName;

    private String settingRemark;
}