package com.apec.information.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/7/18 20:41
 **/
@Getter
@Setter
@ToString
public class CategoryDTO
{
    /** ID */
    private String id;

    /**
     * 组织编号
     */
    private String orgNo;

    /**
     * 编号
     */
    private String categoryNo;

    /**
     * 分类名称
     */
    private String categoryName;

    private String parentNo;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 选用模块(1：普通文章，2：快讯)
     */
    private Integer module;

    /**
     * 是否默认（0：默认  1：不默认）
     */
    private Integer def;

    /**
     * APP是否可见（0：可见  1：不可见）
     */
    private Integer visiableApp;

    /**
     * 管理后台是否可见（0：可见  1：不可见）
     */
    private Integer visiableMana;

    /**
     * 外部编号
     */
    private String externalNo;
    /**
     * 商家编号
     */
    private String merchantId;
    /**
     * 商家编号列表
     */
    private List<String> merchantIds;
}