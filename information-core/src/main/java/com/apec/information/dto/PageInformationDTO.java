package com.apec.information.dto;

import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.dto.PageDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资讯记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Data
public class PageInformationDTO extends BaseDTO
{
    private Date startDate;

    private Date endDate;

    private String id;

    private String orgNo;

    private String categoryNo;

    private Boolean collect;

    private String userNo;

    private Integer informationModule;

    private String informationType;

    private String informationSource;

    private String informationSourceName;

    private Boolean informationEditable;

    private String informationPublicStatus;

    private String informationName;

    private String informationTitle;

    private String informationIssuer;

    private String informationSummary;

    private List<String> informationLabel;

    private Boolean existImage;

    private String informationRemark;

    private String merchantId;
}