/*
 *
 *   @copyright      Copyright © 2017 - 2018. [深圳市中农易讯信息技术有限公司] All rights reserved.
 *   @project        APEC_CJ212_pages
 *   <AUTHOR>
 *   @date           2017-09-16 14:04:57
 *
 *   @lastModifie  2017-09-16 14:04:57
 *
 */

package com.apec.information.dto;

import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.information.enumtype.LinkTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 *
 * @DESC: CmsRichTextDTO
 */
@Getter
@Setter
public class CmsRichTextDTO extends BaseDTO
{

    /** ID */
    private String id;

    /** 编号 */
    private String richTextNo;

    /** 页面名称 */
    private String richTextName;

    /** 页面标题名称 */
    private String richTextTitle;

    /** 页面链接 */
    private String richTextUrl;

    /** 链接类型 */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    private LinkTypeEnum linkType;

    /** 发布人 */
    private String publishMan;

    private String userOrg;

    /**
     * 图片
     */
    @Deprecated
    private String richCoverImg;

    /**
     * 图片
     */
    private List<String> imageList;

    /** 类型 */
    private String richTextType;

    private String richTextTypeName;

    /** 内容 */
    private String richTextContent;

    private String pureContent;

    private String status;

    /** 备注 */
    private String remark;

    private EnableFlag enableFlag;

    private boolean editable = true;

    /** 创建时间 */
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createBy;

    private String lastUpdateBy;

    /**
     * 来源
     */
    private String source;

    /**
     * 访问量
     */
    private Long viewCount;

    /**
     * 点赞用户的ID
     */
    private Set<String> userIds;

    /**
     * 点赞用户id
     */
    private String userId;

    /**
     *  点赞数量
     */
    private long thumbsUpCount;

    /**
     * 是否点赞
     */
    private boolean flagThumbs;
}