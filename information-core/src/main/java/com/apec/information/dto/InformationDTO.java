package com.apec.information.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资讯记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Data
public class InformationDTO
{
    private String id;

    private Date createDate;

    private String orgNo;

    private String orgName;

    private String categoryNo;

    private String categoryName;

    private String informationNo;

    private String informationSource;

    private String informationSourceName;

    private Boolean informationEditable;

    private Integer informationModule;

    private String informationPublicStatus;

    private String informationName;

    private String informationTitle;

    private String informationIssuer;

    private String informationSummary;

    private String informationContent;

    private String informationCoverImage;

    private List<String> informationImageList;

    private List<String> informationLabel;

    private String informationRemark;

    private String merchantId;

    private String merchantName;

    private List<String> categoryNos;
}