package com.apec.information.util;

import org.springframework.util.Assert;

import java.util.Random;

/**
 * <AUTHOR>
 * @description 编号规则
 * 000：沐甜 1：中农网 10303：茧丝 10401：易果
 * 新增分类时，根据上级个规则延续下一级。
 * 例如：
 * 上级为：000 ； 那么下级规则为000-001
 * 上级为：000-001 ；那么下级为000-001-001
 * @date 2018/7/31 9:20
 **/
public class NumberRule {
    /**
     * 递增的
     * @param lastNumber 插入的最后一个编号，
     * @return
     */
    public static String getNumberRule(String lastNumber) throws Exception {
        Assert.hasLength(lastNumber, "最后一个编号不为空");
        String[] split = lastNumber.split("-");
        int number = Integer.parseInt(split[split.length - 1]) + 1;
        if (0 < number / 10) {
            return "-0" + number;
        }
        return "-00" + number;
    }
}
