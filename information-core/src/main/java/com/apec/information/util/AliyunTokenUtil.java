package com.apec.information.util;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * 获取aliyun sts 临时token
 */
public class AliyunTokenUtil {

    private final static Log log = LogFactory.getLog(AliyunTokenUtil.class);

    public static AssumeRoleResponse.Credentials getToken(String accessKeyId, String accessKeySecret, String endpoint, String roleArn, String roleSessionName) {
        try {
            // 添加endpoint（直接使用STS endpoint，前两个参数留空，无需添加region ID）
            DefaultProfile.addEndpoint("", "", "Sts", endpoint);
            // 构造default profile（参数留空，无需添加region ID）
            IClientProfile profile = DefaultProfile.getProfile("", accessKeyId, accessKeySecret);
            // 用profile构造client
            DefaultAcsClient client = new DefaultAcsClient(profile);
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setMethod(MethodType.POST);
            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleSessionName);
            final AssumeRoleResponse response = client.getAcsResponse(request);
            log.info("Expiration: " + response.getCredentials().getExpiration());
            log.info("Access Key Id: " + response.getCredentials().getAccessKeyId());
            log.info("Access Key Secret: " + response.getCredentials().getAccessKeySecret());
            log.info("Security Token: " + response.getCredentials().getSecurityToken());
            log.info("RequestId: " + response.getRequestId());
            return response.getCredentials();
        } catch (ClientException e) {
            System.err.println("get sts token failed :");
            System.err.println("Error code: " + e.getErrCode());
            System.err.println("Error message: " + e.getErrMsg());
            System.err.println("RequestId: " + e.getRequestId());
        }
        return null;
    }

    /*public static void main(String[] args) {
        String Endpoint = "sts.aliyuncs.com";
        String accessKeyId = "LTAIbj3N6N1c1a3d";
        String accessKeySecret = "J8YGX0Xc6Syi7HRy5uwIfIMAYtUAou";
        String roleArn = "acs:ram::10376078:role/aliyunosstokengeneratorrole";
        String roleSessionName = "session-name";
        AssumeRoleResponse.Credentials response = getToken(accessKeyId, accessKeySecret, Endpoint, roleArn, roleSessionName);
        System.out.println("Expiration: " + response.getExpiration());
        System.out.println("Access Key Id: " + response.getAccessKeyId());
        System.out.println("Access Key Secret: " + response.getAccessKeySecret());
        System.out.println("Security Token: " + response.getSecurityToken());

    }*/
}
