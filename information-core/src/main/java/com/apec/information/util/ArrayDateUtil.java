package com.apec.information.util;

import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.DateUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @date 2018-03-26 17:01
 * <AUTHOR>
 */
public class ArrayDateUtil
{
    public static List<String> get(Date startDate, Date endDate)
    {
        List<String> list = Lists.newArrayList();
        Date temp = (Date)startDate.clone();
        while (temp.before(endDate))
        {
            list.add(ymdToString(temp));
            temp.setTime(temp.getTime() + ONE_DAY);
        }
        return list;
    }

    public static String ymdToString(Date date)
    {
        if(null == date)
        {
            return null;
        }
        return ymd.format(date);
    }

    public static String ymdhmsToString(Date date)
    {
        if(null == date)
        {
            return null;
        }
        return ymdhms.format(date);
    }

    public static Date ymdhmsToDate(String date)
    {
        try
        {
            return ymdhms.parse(date);
        }
        catch (Exception e)
        {
            return null;
        }
    }

    public static Date nextDate(Date date, Integer day)
    {
        if(null == date)
        {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, day);
        return c.getTime();
    }

    public static Date thisDay()
    {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    private static long ONE_DAY = 86400000;

    private static SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");

    private static SimpleDateFormat ymdhms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
}