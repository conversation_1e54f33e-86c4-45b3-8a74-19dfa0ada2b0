/*
 *
 *   @copyright      Copyright © 2017 - 2018. [深圳市中农易讯信息技术有限公司] All rights reserved.
 *   @project        APEC_CJ212_pages
 *   <AUTHOR>
 *   @date           2017-09-16 14:04:57
 *
 *   @lastModifie  2017-09-16 14:04:57
 *
 */

package com.apec.information.util;

import java.util.Collection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;

/**

 * 类名称： CommonTools.class
 * 内容摘要：判断字符串，集合，map是否为空的工具类
 * 完成日期：2017/05/24 16:41
 * 编码作者：warne
 */
public abstract class CommonTools
{

    /**
     * //# check str whether is null or length=0
     *
     * @param str
     */
    public static boolean isEmpty(String str)
    {
        return str == null || str.length() == 0;
    }

    /**
     * //# check str whether is not null and length !=0
     *
     * @param str
     */
    public static boolean isNotEmpty(String str)
    {
        return !StringUtils.isEmpty(str);
    }

    /**
     * //# check str whether is null or length !=0 or include space
     *
     * @param str
     */
    public static boolean isBlank(String str)
    {
        int strLen;
        if(str == null || (strLen = str.length()) == 0)
        {
            return true;
        }
        for(int i = 0; i < strLen; i++)
        {
            if((Character.isWhitespace(str.charAt(i)) == false))
            {
                return false;
            }
        }
        return true;
    }

    /**
     * //# check str whether is null and length !=0 and include space
     *
     * @param str
     */
    public static boolean isNotBlank(String str)
    {
        return !isBlank(str);
    }

    /**
     * //# generic, judge an array whether is null or its length=0
     *
     * @param <T>
     * @param array
     * @return boolean
     */
    public static <T> boolean isEmpty(T[] array)
    {
        return array == null || array.length == 0;
    }

    /**
     * //# generic, judge an array whether is not null and its length !=0
     *
     * @param <T>
     * @param array
     * @return boolean
     */
    public static <T> boolean isNotEmpty(T[] array)
    {
        return !isEmpty(array);
    }

    /**
     * //# judge a collection whether is null or empty
     *
     * @param collection
     * @return boolean
     */
    public static boolean isEmpty(Collection<?> collection)
    {
        return collection == null || collection.isEmpty();
    }

    /**
     * //# judge a collection whether is not null and non-null
     *
     * @param collection
     * @return boolean
     */
    public static boolean isNotEmpty(Collection<?> collection)
    {
        return !isEmpty(collection);
    }

    /**
     * //# generic, judge an map whether is not null and its length !=0
     *
     * @param map
     * @return boolean
     */
    public static boolean isEmpty(Map<?, ?> map)
    {
        return map == null || map.isEmpty();
    }

    /**
     * //# judge a map whether is not null and non-null
     *
     * @param map
     * @return boolean
     */
    public static boolean isNotEmpty(Map<?, ?> map)
    {
        return !isEmpty(map);
    }

    /**
     * bean copy
     * @param dest
     * @param orig
     */
    public static void copy(Object dest, Object orig)
    {
        try
        {
            BeanUtils.copyProperties(dest, orig);
        }
        catch (Exception e)
        {
            logger.error(" --- bean copy fail! dest={} , orig={} \n desc: {}",
                         dest.toString(), orig.toString(), e.toString());
        }
    }

    protected static final Logger logger = LoggerFactory.getLogger(CommonTools.class);

}

