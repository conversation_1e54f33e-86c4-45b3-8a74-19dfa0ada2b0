//package com.apec.information.util;
//
//import org.springframework.dao.DataAccessException;
//import org.springframework.data.redis.connection.RedisConnection;
//import org.springframework.data.redis.core.RedisCallback;
//import org.springframework.data.redis.core.RedisTemplate;
//import redis.clients.jedis.JedisCluster;
//import redis.clients.jedis.JedisCommands;
//import redis.clients.jedis.Protocol;
//import redis.clients.util.SafeEncoder;
//
//import java.util.concurrent.TimeUnit;
//
///**
// *
// */
//public class RedisLockUtil {
//
//    private RedisTemplate redisTemplate;
//    private JedisCluster jedisCluster = null;
//
//    public RedisLockUtil(RedisTemplate redisTemplate, JedisCluster jedisCluster) {
//        this.jedisCluster = jedisCluster;
//        this.redisTemplate = redisTemplate;
//    }
//
//    public RedisLockUtil(JedisCluster jedisCluster) {
//        this.jedisCluster = jedisCluster;
//    }
//
//
//    /**
//     * 加锁，只加锁一次
//     *
//     * @param key        key
//     * @param expireTime 过期时间
//     * @param timeUnit   类型ms s
//     * @return 是否加锁成功
//     */
//    public boolean lock(String key, Long expireTime, final TimeUnit timeUnit) {
//        // setnx成功返回true
//        return setNxAndExpire(key, expireTime, timeUnit) != null;
//
//    }
//
//    /**
//     * 加锁，多次获取锁，设置一个过期时间，在最坏的情况下自动过期，不会存在死锁。
//     *
//     * @param key           key
//     * @param expireTime    过期时间
//     * @param timeUnit      时间类型
//     * @param retry         重试次数
//     * @param retryInterval 重试间隔 小于1000ms，大于则给250ms
//     * @return
//     */
//    public boolean lock(String key, Long expireTime, final TimeUnit timeUnit, int retry, int retryInterval) {
//        boolean isLock = false;
//        if (retry < 0) {
//            retry = 3;
//        }
//        if (1000 < retryInterval) {
//            retryInterval = 250;
//        }
//        while (0 < retry) {
//            // 加锁
//            isLock = setNxAndExpire(key, expireTime, timeUnit) != null;
//            if (isLock) {
//                // 获取锁成功
//                return isLock;
//            }
//            retry--;
//            try {
//                Thread.sleep(retryInterval);
//            } catch (InterruptedException e) {
//            }
//
//        }
//        return isLock;
//
//    }
//
//    public boolean lock(String key, int retry, int retryInterval, String value) {
//        boolean isLock = false;
//        if (retry < 0) {
//            retry = 3;
//        }
//        if (1000 < retryInterval) {
//            retryInterval = 250;
//        }
//        while (0 < retry) {
//            // 加锁
//            isLock = setNxLock(key, value);
//            if (isLock) {
//                // 获取锁成功
//                return isLock;
//            }
//            retry--;
//            try {
//                Thread.sleep(retryInterval);
//            } catch (InterruptedException e) {
//            }
//
//        }
//        return isLock;
//
//    }
//
//    /**
//     * 集群删除key
//     *
//     * @param key
//     * @return
//     */
//    public void releaseLock(String key) {
//        jedisCluster.del(key);
//    }
//
//    /**
//     * 单个
//     *
//     * @param key
//     */
//    public void releaseLockTmplate(String key) {
//        redisTemplate.delete(key);
//    }
//
//
//    /**
//     * 自动加一个过期时间，防止没有删掉key,就会死锁。这个过期时间适度度量
//     * 集群不适用，要换成lua  脚本
//     *
//     * @param key
//     * @param expireTime
//     * @param timeUnit
//     * @return
//     */
//    private Object setNxAndExpire(String key, Long expireTime, final TimeUnit timeUnit) {
//
//        return redisTemplate.execute(new RedisCallback<Object>() {
//            @Override
//            public Object doInRedis(RedisConnection connection) throws DataAccessException {
//                return connection.execute("set", new byte[][]{
//                        SafeEncoder.encode(key),
//                        SafeEncoder.encode("lockTime:" + System.currentTimeMillis() + ";expire:" + timeUnit.toMillis(expireTime)),
//                        SafeEncoder.encode("nx"),
//                        SafeEncoder.encode("ex"),
//                        Protocol.toByteArray(timeUnit.toMillis(expireTime))});
//            }
//        });
//
//    }
//
//    /**
//     * 不保险，最好再配上过期时间
//     *
//     * @param key
//     * @param value
//     * @return
//     */
//    private boolean setNxLock(String key, String value) {
//
//        Long setnx = jedisCluster.setnx(key, value);
//        if (0 == setnx) {
//            return false;
//        }
//        return true;
//
//    }
//
//}
