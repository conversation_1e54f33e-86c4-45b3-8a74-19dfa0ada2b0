package com.apec.information.base;

import com.apec.framework.base.BaseController;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.dto.BaseDTO;
import com.apec.framework.common.model.PageJSON;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.common.vo.UserInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import static com.apec.framework.common.constant.FrameConsts.PREFIX_SESSIONID;
import static com.apec.framework.common.constant.FrameConsts.SESSION_ID;
import static com.apec.information.constant.InformationConstants.*;
import static java.lang.Boolean.FALSE;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 * @DESC:
 */
public class MyControll<PERSON> extends BaseController
{
    protected PageRequest getPageRequestSort(BaseSortDTO dto)
    {
        Sort.Direction direction = StringUtils.isBlank(dto.getSortType()) ?
            Sort.Direction.DESC :
            Sort.Direction.valueOf(dto.getSortType());
        String sortColumn = StringUtils.isBlank(dto.getSortColumn()) ?
            "createDate" :
            dto.getSortColumn();
        Sort sort = new Sort(direction, sortColumn);
        int currentNo = 1;
        int pageSize = 10;
        if(dto.getPageNumber() > 0)
        {
            currentNo = dto.getCurrentNo();
        }
        if(dto.getPageSize() > 0 && dto.getPageSize() < FrameConsts.NUMBER_FIVE_HUNDRED)
        {
            pageSize = dto.getPageSize();
        }
        dto.setCurrentNo(currentNo);
        dto.setPageSize(pageSize);
        return new PageRequest(currentNo - 1, pageSize, sort);
    }

    protected BaseUserInfo getBaseUserInfo(String jsonStr)
    {
        BaseUserInfo userInfo = new BaseUserInfo();
        PageJSON<Object> pageJSON = getPageJSON(jsonStr, Object.class);
        if(null != pageJSON && null != pageJSON.getRequestAttrMap())
        {
            String userInfoJson = (String)pageJSON.getRequestAttrMap().get(FrameConsts.USER_INFO);
            if(StringUtils.isNotBlank(userInfoJson))
            {
                userInfo = JsonUtils.parseObject(userInfoJson, BaseUserInfo.class);
            }
        }
        return userInfo;
    }

    /**
     * 成功
     * @param data
     * @return
     */
    public <T> String success(T data)
    {
        return getResultJSONStr(SUCCESS_BOOL, data, SUCCESS_STATUS);
    }

    /**
     * 失败
     * @param errorCode
     * @param errorMsg
     * @return
     */
    public String fail(String errorCode, String errorMsg)
    {
        return getResultJSONStr(FALSE, NULL_STR, errorCode, errorMsg);
    }
}