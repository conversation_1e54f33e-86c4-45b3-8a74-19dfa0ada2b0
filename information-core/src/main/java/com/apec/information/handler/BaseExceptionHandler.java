package com.apec.information.handler;

import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.SpringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2018-09-17
 */
@ControllerAdvice
public class BaseExceptionHandler
{
    private static org.slf4j.Logger logger = LoggerFactory.getLogger(BaseExceptionHandler.class);

    @ExceptionHandler(value = InformationException.class)
    @ResponseBody
    public ResultData<String> handleOrderError(HttpServletRequest req, InformationException e)
    {
        ResultData<String> rd = new ResultData<>();
        rd.setSucceed(false);
        String errorCode = e.getErrorCode();
        rd.setErrorCode(errorCode);
        String message = SpringUtils.getStringMessage(errorCode, e.getArgs());
        rd.setErrorMsg(message);
        String errorMessage = e.getErrorMessage();
        if(null == errorMessage)
        {
            errorMessage = message;
        }
        logger.error("Unhanddled GoodsPoolException! //// " + e.getErrorCode() + " " + errorMessage, e);
        return rd;
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ResultData<Object> handleOrderError(HttpServletRequest req, Exception e)
    {
        ResultData<Object> rd = new ResultData<>();
        rd.setSucceed(false);
        rd.setErrorCode("201500");
        rd.setErrorMsg("服务内部错误");
        logger.error("Unhanddled Exception! //// " + e.getMessage(), e);
        return rd;
    }

}