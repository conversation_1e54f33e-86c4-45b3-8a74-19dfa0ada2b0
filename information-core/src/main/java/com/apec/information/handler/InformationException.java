package com.apec.information.handler;

public class InformationException extends RuntimeException
{
    /**
     * 
     */
    private static final long serialVersionUID = 7396508137351281670L;

    private String errorCode;

    private String errorMessage;
    
    private Object[] args;

    public InformationException()
    {

    }

    public InformationException(String code, Object... args)
    {
        this.errorCode = code;
        this.args = args;
    }

    public InformationException(Exception ex)
    {
        if(ex instanceof InformationException)
        {
            InformationException e=(InformationException)ex;
            this.errorCode=e.getErrorCode();
            this.args=e.getArgs();
            this.errorMessage=e.getErrorMessage();
        }
        else
        {
            this.errorCode = "201001";
            this.errorMessage = ex.getMessage();
        }        
    }
    
    public InformationException(String code)
    {
        this.errorCode = code;
    }

    public String getErrorCode()
    {
        return errorCode;
    }

    public void setErrorCode(String errorCode)
    {
        this.errorCode = errorCode;
    }

    public String getErrorMessage()
    {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage)
    {
        this.errorMessage = errorMessage;
    }

    public Object[] getArgs()
    {
        return args;
    }

    public void setArgs(Object[] args)
    {
        this.args = args;
    }
}
