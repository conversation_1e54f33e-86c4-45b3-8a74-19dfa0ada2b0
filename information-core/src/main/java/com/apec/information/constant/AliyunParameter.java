package com.apec.information.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2018/7/26 17:34
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun", locations = "classpath:application.properties")
public class AliyunParameter
{
    private String endpoint;

    private String accessKeyId;

    private String accessKeySecret;

    private String roleArn;

    private String roleSessionName;
}