package com.apec.picture.web;

import com.apec.information.base.MyController;
import com.apec.information.handler.InformationException;
import com.apec.information.dto.ImageInfoDTO;
import com.apec.picture.service.PictureService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.net.MalformedURLException;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 * @DESC: CmsRichTextController
 */

@RestController
@RefreshScope
@RequestMapping(value = "/jobs")
public class JobsController extends MyController
{
    private static Logger logger = LoggerFactory.getLogger(JobsController.class);

    @Autowired
    private PictureService pictureService;

    /**
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/checkAndUpload", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String checkAndUpload(@RequestBody String jsonStr) throws Exception
    {
        logger.info("我有一群链接，我要上传到阿里云，但不想重复，{}", jsonStr);
        ImageInfoDTO imageDTO = getFormJSON(jsonStr, ImageInfoDTO.class);
        try
        {
            return success(pictureService.checkAndUpload(imageDTO));
        }
        catch (MalformedURLException e)
        {
            logger.error("上传阿里云失败，", e);
            throw new InformationException("10001");
        }
    }
}