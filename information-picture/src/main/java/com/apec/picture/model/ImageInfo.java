package com.apec.picture.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * @date 2018-10-12
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Entity
@Table(name = "information_image_info")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class ImageInfo extends BaseModel<String>
{
    /**
     * 所属组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 编号
     */
    @Column(name = "IMAGE_SOURCE_URL")
    private String imageSourceUrl;

    /**
     * 分类名称
     */
    @Column(name = "IMAGE_ALIYUN_URL")
    private String imageAliyunUrl;
}