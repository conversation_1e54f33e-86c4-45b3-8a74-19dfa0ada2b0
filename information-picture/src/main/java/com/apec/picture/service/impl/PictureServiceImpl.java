package com.apec.picture.service.impl;

import com.aliyun.oss.OSSClient;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.picture.dao.ImageInfoDAO;
import com.apec.picture.model.ImageInfo;
import com.apec.information.dto.ImageInfoDTO;
import com.apec.picture.model.QImageInfo;
import com.apec.picture.service.PictureService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.invoke.MethodHandles;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.apec.information.constant.InformationConstants.DEFAULT_CREATE_BY;

/**
 * @date 2018-10-12
 * <AUTHOR>
 */
@Service
public class PictureServiceImpl implements PictureService
{
    @Override
    public List<String> checkAndUpload(ImageInfoDTO imageDTO) throws Exception
    {
        List<String> sourceList = imageDTO.getSourceList();
        if(CollectionUtils.isEmpty(sourceList))
        {
            return sourceList;
        }
        List<String> result = Lists.newArrayListWithCapacity(sourceList.size());
        BooleanExpression predicate = QImageInfo.imageInfo.imageSourceUrl.in(sourceList);
        List<ImageInfo> list = (List<ImageInfo>)imageInfoDAO.findAll(predicate);
        if(sourceList.size() < list.size())
        {
            LOG.error("出错，原url少于已知url");
            throw new Exception("原url少于已知url");
        }
        if(sourceList.size() == list.size())
        {
            list.forEach(imageInfo -> result.add(imageInfo.getImageAliyunUrl()));
            return result;
        }
        Map<String, String> map = Maps.newHashMap();
        list.forEach(imageInfo -> map.put(imageInfo.getImageSourceUrl(), imageInfo.getImageAliyunUrl()));

        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        for(String source : sourceList)
        {
            String aliyunUrl = map.get(source);
            if(StringUtils.isNotBlank(aliyunUrl))
            {
                result.add(aliyunUrl);
                continue;
            }
            // 上传网络流。
            try
            {
                InputStream inputStream = getInputStream(source);
                String suffix = getSuffix(source);
                String objectName = keyGen.nextId() + "_" + suffix;
                ossClient.putObject(bucketName, objectName, inputStream);
                aliyunUrl = finalPerfix + objectName;
                createImage(source, aliyunUrl);
                inputStream.close();
            }
            catch (Exception e)
            {
                LOG.error("上传阿里云失败，当前默认返回原url：", e);
                aliyunUrl = source;
            }
            result.add(aliyunUrl);
        }
        LOG.info("上传图片数量：{}", sourceList.size());
        // 关闭OSSClient。
        ossClient.shutdown();
        return result;
    }

    private InputStream getInputStream(String source) throws IOException
    {
        URL url = new URL(source);
        URLConnection urlConnection = url.openConnection();
        urlConnection.setRequestProperty("User-Agent",
                                         "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; CIBA)");
        return urlConnection.getInputStream();
    }

    private static final Logger LOG = LoggerFactory.getLogger(MethodHandles.lookup().lookupClass());

    @Value("${aliyun.endpoint}")
    private String endpoint;

    @Value("${aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.bucketName}")
    private String bucketName;

    @Value("${aliyun.finalPerfix}")
    private String finalPerfix;

    @Autowired
    private SnowFlakeKeyGen keyGen;

    @Autowired
    private ImageInfoDAO imageInfoDAO;

    private void createImage(String source, String aliyunUrl)
    {
        ImageInfo imageInfo = new ImageInfo();
        Date now = new Date();
        imageInfo.setId(Long.toString(keyGen.nextId()));
        imageInfo.setImageSourceUrl(source);
        imageInfo.setImageAliyunUrl(aliyunUrl);
        imageInfo.setCreateBy(DEFAULT_CREATE_BY);
        imageInfo.setCreateDate(now);
        imageInfo.setLastUpdateBy(DEFAULT_CREATE_BY);
        imageInfo.setLastUpdateDate(now);
        imageInfoDAO.save(imageInfo);
    }

    private static String getSuffix(String source)
    {
        String suffixes = "jpeg|gif|jpg|png|pdf|rar|zip";
        Pattern pat = Pattern.compile("[\\w]+[\\.](" + suffixes + ")");
        //正则判断
        Matcher mc = pat.matcher(source);
        while (mc.find())
        {
            return mc.group();
        }
        return "jpg";
    }

    //    public static void main(String[] args) throws IOException
    //    {
    //        URL url = new URL("http://www.gzw.net/uploadfile/2018/1011/20181011024149980.jpg");
    //        URLConnection urlConnection = url.openConnection();
    //        urlConnection.setRequestProperty("User-Agent",
    //                                         "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0; CIBA)");
    //        InputStream inputStream = urlConnection.getInputStream();
    //        OutputStream outputStream = new FileOutputStream("C:/Users/<USER>/Desktop/abc.jpg");
    //        byte[] bytes = new byte[1024];
    //        while (0 < inputStream.read(bytes))
    //        {
    //            outputStream.write(bytes);
    //        }
    //        outputStream.flush();
    //        inputStream.close();
    //        outputStream.close();
    //    }
}