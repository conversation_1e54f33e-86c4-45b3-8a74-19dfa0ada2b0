server.port=23212
logging.level.com.apec=DEBUG

#mongodb uri
spring.data.mongodb.uri=mongodb://*************:27017/cms

eureka.client.serviceUrl.defaultZone=http://**************:1111/eureka/

spring.datasource.primary.url=*******************************************************************************************************************
spring.datasource.primary.username=znw
spring.datasource.primary.password=znw@ap-ec

redis.database=8
redis.host=**************
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=**************
spring.redis.port=7000
spring.redis.host2=**************
spring.redis.port2=7001
spring.redis.nodes=
spring.redis.timeout=5000
spring.redis.maxIdle=8
spring.redis.minIdle=4
spring.redis.maxRedirections=3000

mt.api.baseurl=http://**************:16002

spring.rabbitmq.host=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/dztest