<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>information-parent</artifactId>
    <version>1.0.1-RELEASE</version>
    <packaging>pom</packaging>

    <name>information-parent</name>

    <parent>
        <artifactId>framework-parent</artifactId>
        <groupId>com.apec</groupId>
        <version>1.5.0-RELEASE</version>
    </parent>

    <properties>
        <log4j2.version>2.15.0</log4j2.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Brixton.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 包含的子工程maven模块 -->
    <modules>
        <module>information-core</module>
        <module>information-comment</module>
        <module>information-category</module>
        <!--<module>information-login</module>-->
        <module>information-service</module>
        <module>information-setting</module>
        <module>information-statistics</module>
        <module>information-statsjob</module>
        <module>information-jobs</module>
        <module>information-picture</module>
    </modules>
</project>