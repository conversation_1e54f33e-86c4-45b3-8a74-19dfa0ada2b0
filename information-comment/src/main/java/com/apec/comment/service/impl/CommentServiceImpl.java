package com.apec.comment.service.impl;

import com.apec.comment.model.*;
import com.apec.comment.service.CommentService;
import com.apec.framework.cache.CacheService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.dto.CommentDTO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.information.enumtype.ShowStatus;
import com.apec.information.handler.InformationException;
import com.apec.information.model.InformationCommentModel;
import com.apec.information.vo.AppCommentReplyVO;
import com.apec.information.vo.AppCommentVO;
import com.apec.information.vo.CommentReplyVO;
import com.apec.information.vo.CommentVO;
import com.google.common.collect.Lists;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.apec.information.constant.InformationConstants.CACHE_COMMENT;
import static com.apec.information.constant.InformationConstants.CACHE_COMMENT_EDIT;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
@Service
public class CommentServiceImpl implements CommentService
{
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void examine(CommentDTO commentDTO, String modify)
    {
        String id = commentDTO.getId();
        Query query = Query
            .query(Criteria.where("enableFlag").is(EnableFlag.Y)
                       .orOperator(Criteria.where("replyList." + id).exists(true), Criteria.where("id").is(id)));
        logger.info(JsonUtils.toJSONString(query));
        InformationCommentModel comment = mongoTemplate.findOne(query, InformationCommentModel.class);
        if(null == comment)
        {
            throw new InformationException("25014001");
        }
        // 如果审核的是评论本身
        if(id.equals(comment.getId()))
        {
            comment.setShowStatus(commentDTO.getShowStatus());
            comment.setLastUpdateBy(modify);
            comment.setLastUpdateDate(new Date());
            mongoTemplate.save(comment);
            return;
        }
        // 如果审核的是回复列表中的某条回复
        InformationCommentReplyModel reply = comment.getReplyList().get(id);
        reply.setShowStatus(commentDTO.getShowStatus());
        reply.setModify(modify);
        comment.getReplyList().put(id, reply);
        if(ShowStatus.PUBLIC == ShowStatus.find(commentDTO.getShowStatus()))
        {
            comment.getShowList().add(id);
            flashCache(comment.getId(), reply);
        }
        mongoTemplate.save(comment);

        //删除管理端缓存
        cacheService.remove(CACHE_COMMENT_EDIT + id);
    }

    @Override
    public void delete(String id, String modify)
    {
        Criteria criteria = new Criteria();
        criteria.orOperator(Criteria.where("id").is(id), Criteria.where("commentMap").is(id));
        Query query = Query.query(criteria);
        InformationCommentModel comment = mongoTemplate.findOne(query, InformationCommentModel.class);
        if(null == comment)
        {
            throw new InformationException("25014001");
        }
        if(id.equals(comment.getId()))
        {
            comment.setEnableFlag(EnableFlag.N);
            comment.setLastUpdateBy(modify);
            comment.setLastUpdateDate(new Date());
            mongoTemplate.save(comment);
            return;
        }
        InformationCommentReplyModel reply = comment.getReplyList().get(id);
        reply.setEnableFlag(EnableFlag.N);
        reply.setModify(modify);
        comment.getReplyList().put(id, reply);
        comment.getShowList().remove(id);
        comment.getDeleteList().add(id);
        mongoTemplate.save(comment);
    }

    @Override
    public PageDTO<CommentVO> page(CommentQueryDTO commentDTO, PageRequest pageRequest)
    {
        PageDTO<CommentVO> result = new PageDTO<>();
        Query query = Query.query(Criteria.where("enableFlag").is(EnableFlag.Y));
        if(StringUtils.isNotBlank(commentDTO.getInformationId()))
        {
            query.addCriteria(Criteria.where("informationId").is(commentDTO.getInformationId()));
        }
        if (StringUtils.isNotBlank(commentDTO.getInformationTitle())) {
            query.addCriteria(Criteria.where("informationTitle").regex("^.*"+commentDTO.getInformationTitle()+".*$"));
        }
        if (StringUtils.isNotBlank(commentDTO.getComment())) {
            query.addCriteria(Criteria.where("comment").regex("^.*"+commentDTO.getComment()+".*$"));
        }
        Long count = mongoTemplate.count(query, InformationCommentModel.class);
        int startRow = commentDTO.getPageSize() * (commentDTO.getPageNumber() - 1);
        Query createDate = query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createDate")));
        List<InformationCommentModel> page = mongoTemplate
            .find(createDate.skip(startRow).limit(commentDTO.getPageSize()), InformationCommentModel.class);
        List<CommentVO> dtoList = Lists.newArrayListWithCapacity(page.size());
        CommentVO target;
        for(InformationCommentModel comment : page)
        {
            target = flashEditCache(comment);
            dtoList.add(target);
        }
        result.build(count, pageRequest, dtoList);
        return result;
    }

    private static final String[] IGNORE_FLEIDS = ArrayUtils.toArray("replyList");

    @Value("${session.time.out}")
    private Integer sessionTime;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CacheService cacheService;

    private CommentVO flashEditCache(InformationCommentModel comment)
    {
        String key = CACHE_COMMENT_EDIT + comment.getId();
        String value = cacheService.get(key);
        if(StringUtils.isNotBlank(value))
        {
            return JsonUtils.parseObject(value, CommentVO.class);
        }
        CommentVO result = new CommentVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(comment, result, IGNORE_FLEIDS);
        LinkedHashMap<String, InformationCommentReplyModel> map = comment.getReplyList();
        List<CommentReplyVO> replyList = Lists.newArrayListWithCapacity(comment.getShowList().size());
        for(Map.Entry<String, InformationCommentReplyModel> entrySet : map.entrySet())
        {
            InformationCommentReplyModel reply = entrySet.getValue();
            if(EnableFlag.Y != reply.getEnableFlag())
            {
                continue;
            }
            CommentReplyVO temp = new CommentReplyVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(reply, temp);
            temp.setId(entrySet.getKey());
            replyList.add(temp);
        }
        result.setReplyList(replyList);
        cacheService.add(key, JsonUtils.toJSONString(result), sessionTime);
        return result;
    }

    private void flashCache(String id, InformationCommentReplyModel reply)
    {
        //更新客户端缓存
        String key = CACHE_COMMENT + id;
        String value = cacheService.get(key);
        if(StringUtils.isBlank(value))
        {
            return;
        }
        AppCommentVO appCommentVO = JsonUtils.parseObject(value, AppCommentVO.class);
        AppCommentReplyVO temp = new AppCommentReplyVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(reply, temp);
        appCommentVO.getReplyList().add(temp);
        cacheService.add(key, JsonUtils.toJSONString(appCommentVO), sessionTime);
    }
}