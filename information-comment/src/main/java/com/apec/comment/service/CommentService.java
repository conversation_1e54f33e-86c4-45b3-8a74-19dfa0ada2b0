package com.apec.comment.service;

import com.apec.information.dto.CommentDTO;
import com.apec.information.vo.CommentVO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.framework.common.dto.PageDTO;
import org.springframework.data.domain.PageRequest;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
public interface CommentService
{
    /**
     * 审核
     * @param commentDTO
     * @param modify
     */
    void examine(CommentDTO commentDTO, String modify);

    /**
     * 删除
     * @param id
     * @param modify
     */
    void delete(String id, String modify);

    /**
     * 分页评论
     * @param commentDTO
     * @param pageRequest
     * @return
     */
    PageDTO<CommentVO> page(CommentQueryDTO commentDTO, PageRequest pageRequest);
}