package com.apec.comment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.apec.comment.model.InformationCommentReplyModel;
import com.apec.comment.service.CommentApiService;
import com.apec.framework.cache.CacheService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.springcloud.SpringCloudClient;
import com.apec.information.dto.BaseId;
import com.apec.information.dto.CommentDTO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.information.enumtype.ShowStatus;
import com.apec.information.handler.InformationException;
import com.apec.information.model.InformationCommentModel;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.AppCommentReplyVO;
import com.apec.information.vo.AppCommentVO;
import com.apec.information.vo.InformationVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.apec.information.constant.InformationConstants.CACHE_COMMENT;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
@Service
public class CommentApiServiceImpl implements CommentApiService
{
    @Override
    public void insert(CommentDTO commentDTO, String modify)
    {
        InformationVO informationVO = getInformation(commentDTO.getInformationId());
        if(null == informationVO)
        {
            throw new InformationException("25013001");
        }
        InformationCommentModel comment = new InformationCommentModel();
        BeanUtils.copyPropertiesIgnoreNullFilds(commentDTO, comment);
        comment.setOrgNo(informationVO.getOrgNo());
        comment.setInformationTitle(informationVO.getInformationTitle());
        comment.setCategoryNo(informationVO.getCategoryNo());
        comment.setShowStatus(ShowStatus.PUBLIC.val());
        Date now = new Date();
        comment.setId(Long.toString(idGen.nextId()));
        comment.setCreateBy(modify);
        comment.setLastUpdateBy(modify);
        comment.setCreateDate(now);
        comment.setLastUpdateDate(now);
        comment.setReplyList(Maps.newLinkedHashMap());
        comment.setShowList(Lists.newArrayList());
        comment.setDeleteList(Lists.newArrayList());
        mongoTemplate.save(comment);
    }

    @Override
    public void reply(CommentDTO commentDTO, String modify)
    {
        InformationVO informationVO = getInformation(commentDTO.getInformationId());
        if(null == informationVO)
        {
            throw new InformationException("25013001");
        }
        InformationCommentModel comment = mongoTemplate
            .findById(commentDTO.getCommentId(), InformationCommentModel.class);
        if(null == comment)
        {
            throw new InformationException("25014001");
        }
        InformationCommentReplyModel reply = new InformationCommentReplyModel();
        BeanUtils.copyPropertiesIgnoreNullFilds(commentDTO, reply);
        reply.setId(Long.toString(idGen.nextId()));
        LinkedHashMap<String, InformationCommentReplyModel> map = comment.getReplyList();
        if(null == map)
        {
            map = Maps.newLinkedHashMap();
        }
        if(StringUtils.isNotBlank(commentDTO.getReplyId()))
        {
            InformationCommentReplyModel replyComment = map.get(commentDTO.getReplyId());
            if(null == replyComment)
            {
                throw new InformationException("25014001");
            }
            reply.setToReplyId(commentDTO.getReplyId());
            reply.setToUserId(replyComment.getUserId());
            reply.setToUserName(replyComment.getUserName());
        }
        reply.setUserId(modify);
        reply.setShowStatus("1");
        reply.setCreateDate(new Date());
        map.put(reply.getId(), reply);
        comment.getShowList().add(reply.getId());
        comment.setReplyList(map);
        mongoTemplate.save(comment);
        reflashCache(comment);
    }

    @Override
    public void delete(String id, String modify)
    {
        Query query = Query.query(new Criteria().orOperator(
            Criteria.where("replyList." + id).exists(true),
            Criteria.where("id").is(id)));
        InformationCommentModel comment = mongoTemplate.findOne(query, InformationCommentModel.class);
        if(null == comment)
        {
            throw new InformationException("25014001");
        }
        if(id.equals(comment.getId()))
        {
            if(!modify.equals(comment.getUserId()))
            {
                throw new InformationException("25014002");
            }
            comment.setEnableFlag(EnableFlag.N);
            comment.setLastUpdateBy(modify);
            comment.setLastUpdateDate(new Date());
            mongoTemplate.save(comment);
            return;
        }
        InformationCommentReplyModel reply = comment.getReplyList().get(id);
        if(!modify.equals(reply.getUserId()))
        {
            throw new InformationException("25014002");
        }
        reply.setEnableFlag(EnableFlag.N);
        reply.setModify(modify);
        comment.getReplyList().put(id, reply);
        comment.getShowList().remove(id);
        comment.getDeleteList().add(id);
        mongoTemplate.save(comment);
    }

    @Override
    public PageDTO<AppCommentVO> page(CommentQueryDTO commentDTO, PageRequest pageRequest, String modify)
    {
        PageDTO<AppCommentVO> result = new PageDTO<>();
        Query query = Query.query(Criteria.where("enableFlag").is(EnableFlag.Y));
        query.addCriteria(Criteria.where("showStatus").is(ShowStatus.PUBLIC.val()));
        query.addCriteria(Criteria.where("informationId").is(commentDTO.getInformationId()));
        Long count = mongoTemplate.count(query, InformationCommentModel.class);
        int startRow = commentDTO.getPageSize() * (commentDTO.getPageNumber() - 1);
        Query createDate = query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createDate")));
        List<InformationCommentModel> page = mongoTemplate
            .find(createDate.skip(startRow).limit(commentDTO.getPageSize()), InformationCommentModel.class);
        List<AppCommentVO> dtoList = Lists.newArrayListWithCapacity(page.size());
        AppCommentVO target;
        for(InformationCommentModel comment : page)
        {
            target = flashCache(comment, modify);
            Set<String> likeList = comment.getLikeList();
            if(null != likeList)
            {
                target.setLiked(likeList.contains(modify));
            }
            dtoList.add(target);
        }
        result.build(count, pageRequest, dtoList);
        return result;
    }

    @Override
    public void like(String commentId, String modify)
    {
        InformationCommentModel comment = mongoTemplate.findById(commentId, InformationCommentModel.class);
        Set<String> likeList = comment.getLikeList();
        if(null == likeList)
        {
            likeList = Sets.newHashSet();
        }
        if(!likeList.remove(modify))
        {
            likeList.add(modify);
        }
        comment.setLikeList(likeList);
        comment.setLikeCount(likeList.size());
        mongoTemplate.save(comment);
        reflashCache(comment);
    }

    @Override
    public PageDTO<AppCommentVO> popular(String informationId, String modify)
    {
        PageDTO<AppCommentVO> result = new PageDTO<>();
        Query query = Query.query(Criteria.where("enableFlag").is(EnableFlag.Y));
        query.addCriteria(Criteria.where("informationId").is(informationId));
        query = query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "likeCount")));
        Long count = mongoTemplate.count(query, InformationCommentModel.class);
        List<InformationCommentModel> list = mongoTemplate
            .find(query.skip(skip).limit(limit), InformationCommentModel.class);
        List<AppCommentVO> dtoList = Lists.newArrayListWithCapacity(limit);
        AppCommentVO target;
        for(InformationCommentModel comment : list)
        {
            target = flashCache(comment, modify);
            Set<String> likeList = comment.getLikeList();
            if(null != likeList)
            {
                target.setLiked(likeList.contains(modify));
            }
            dtoList.add(target);
        }
        result.setRows(dtoList);
        result.setTotalElements(count);
        return result;
    }

    private static final String[] IGNORE_FIELDS = ArrayUtils.toArray("replyList");

    @Value("${information.detail}")
    private String getInformationDetail;

    @Value("${session.time.out}")
    private Integer sessionTime;

    @Value("${popular.skip}")
    private Integer skip;

    @Value("${popular.limit}")
    private Integer limit;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SnowFlakeKeyGen idGen;

    @Autowired
    private SpringCloudClient scc;

    private InformationVO getInformation(String id)
    {
        BaseId baseId = new BaseId();
        baseId.setId(id);
        String jsonStr = scc.post(getInformationDetail, JsonUtils.toJSONString(baseId));
        ResultData<JSONObject> resultData = JsonUtils.parseObject(jsonStr, ResultData.class);
        if(!resultData.isSucceed())
        {
            throw new InformationException("25013001");
        }
        return JSONObject.toJavaObject(resultData.getData(), InformationVO.class);
    }

    private AppCommentVO flashCache(InformationCommentModel comment, String modify)
    {
        String key = CACHE_COMMENT + comment.getId();
        String value = cacheService.get(key);
        if(StringUtils.isNotBlank(value))
        {
            return JsonUtils.parseObject(value, AppCommentVO.class);
        }
        AppCommentVO result = new AppCommentVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(comment, result, IGNORE_FIELDS);
        Map<String, InformationCommentReplyModel> map = comment.getReplyList();
        List<AppCommentReplyVO> replyList = Lists.newArrayListWithCapacity(comment.getShowList().size());
        initReplyList(comment, map, replyList);
        result.setReplyList(replyList);
        cacheService.add(key, JsonUtils.toJSONString(result), sessionTime);
        return result;
    }

    private void reflashCache(InformationCommentModel comment)
    {
        String key = CACHE_COMMENT + comment.getId();
        AppCommentVO result = new AppCommentVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(comment, result, IGNORE_FIELDS);
        Map<String, InformationCommentReplyModel> map = comment.getReplyList();
        List<AppCommentReplyVO> replyList = Lists.newArrayListWithCapacity(comment.getShowList().size());
        initReplyList(comment, map, replyList);
        result.setReplyList(replyList);
        cacheService.add(key, JsonUtils.toJSONString(result), sessionTime);
    }

    private void initReplyList(InformationCommentModel comment, Map<String, InformationCommentReplyModel> map,
        List<AppCommentReplyVO> replyList)
    {
        comment.getShowList().forEach(replyId -> {
            AppCommentReplyVO temp = new AppCommentReplyVO();
            InformationCommentReplyModel reply = map.get(replyId);
            BeanUtils.copyPropertiesIgnoreNullFilds(reply, temp);
            temp.setId(replyId);
            replyList.add(temp);
        });
    }
}