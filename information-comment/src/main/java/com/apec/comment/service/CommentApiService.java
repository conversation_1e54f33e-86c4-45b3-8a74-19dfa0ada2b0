package com.apec.comment.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.dto.CommentDTO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.information.vo.AppCommentVO;
import org.springframework.data.domain.PageRequest;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
public interface CommentApiService
{
    /**
     * 评论
     * @param commentDTO
     * @param modify
     */
    void insert(CommentDTO commentDTO, String modify);

    /**
     * 评论
     * @param commentDTO
     * @param modify
     */
    void reply(CommentDTO commentDTO, String modify);

    /**
     * 删除
     * @param id
     * @param modify
     */
    void delete(String id, String modify);

    /**
     * 分页评论
     * @param commentDTO
     * @param pageRequest
     * @return
     */
    PageDTO<AppCommentVO> page(CommentQueryDTO commentDTO, PageRequest pageRequest, String modify);

    /**
     * 点赞
     * @param commentId
     * @param modify
     * @return
     */
    void like(String commentId, String modify);

    /**
     * 精彩评论
     * @param informationId
     * @param modify
     * @return
     */
    PageDTO<AppCommentVO> popular(String informationId, String modify);
}