package com.apec.comment.web;

import com.apec.information.dto.CommentDTO;
import com.apec.information.vo.CommentVO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.comment.service.CommentService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.information.base.MyController;
import com.apec.information.handler.InformationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
@RestController
@RefreshScope
@RequestMapping(value = "/edit")
public class CommentEditManagerController extends MyController
{
    /**
     * 删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String delete(@RequestBody String jsonStr)
    {
        logger.debug("Delete comment: {}", jsonStr);
        CommentDTO dto = getFormJSON(jsonStr, CommentDTO.class);
        if(StringUtils.isBlank(dto.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        commentService.delete(dto.getId(), modify);
        return success(DELETE_SUCCESS);
    }

    /**
     * 根据ID改页面状态：启用或未启用
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/examine", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String changePublicStatus(@RequestBody String jsonStr)
    {
        logger.debug("Change comment public status：{}", jsonStr);
        CommentDTO commentDTO = getFormJSON(jsonStr, CommentDTO.class);
        if(StringUtils.isBlank(commentDTO.getId()) || null == commentDTO.getShowStatus())
        {
            logger.error("Invalid parameter: id or show status canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        commentService.examine(commentDTO, modify);
        return success(UPDATE_SUCCESS);
    }

    /**
     * 分页查询（管理端）
     * @param jsonStr 用户所属组织
     * @return
     */
    @RequestMapping(value = "page", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String page(@RequestBody String jsonStr)
    {
        logger.debug("Find comment page：{}", jsonStr);
        CommentQueryDTO commentDTO = getFormJSON(jsonStr, CommentQueryDTO.class);
        if(StringUtils.isBlank(commentDTO.getOrgNo()))
        {
            commentDTO.setOrgNo(defaultNode);
        }
        PageRequest pageRequest = genPageRequest(commentDTO);

        PageDTO<CommentVO> result = commentService.page(commentDTO, pageRequest);
        return success(result);
    }

    // ------------------------------ 私有变量及方法 ---------------------------------/

    private static Logger logger = LoggerFactory.getLogger(CommentEditManagerController.class);

    @Value("${default.information.node}")
    private String defaultNode;

    @Autowired
    private CommentService commentService;
}