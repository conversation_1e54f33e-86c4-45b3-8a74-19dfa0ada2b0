package com.apec.comment.web;

import com.apec.information.vo.AppCommentVO;
import com.apec.information.dto.CommentDTO;
import com.apec.information.dto.CommentQueryDTO;
import com.apec.comment.service.CommentApiService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.information.base.MyController;
import com.apec.information.handler.InformationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @date 2018-09-26
 */
@RestController
@RefreshScope
@RequestMapping(value = "/api")
public class CommentApiController extends MyController
{
    /**
     * 新增评论
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String insert(@RequestBody String jsonStr)
    {
        logger.debug("Insert comment: {}", jsonStr);
        CommentDTO commentDTO = getFormJSON(jsonStr, CommentDTO.class);
        try
        {
            Assert.hasLength(commentDTO.getInformationId(), "资讯id不能为空");
            Assert.hasLength(commentDTO.getComment(), "评论内容不能为空");
            Assert.hasLength(commentDTO.getUserName(), "评论人不能为空");
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            commentService.insert(commentDTO, modify);
            return success(INSERT_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Invalid parameter: {}", jsonStr, e);
            throw new InformationException(AI_NO_USERNAME);
        }
        catch (Exception e)
        {
            logger.error("Insert comment error: {}", jsonStr, e);
            throw new InformationException(FIELD_AGAIN);
        }
    }

    /**
     * 新增评论
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/reply", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String reply(@RequestBody String jsonStr)
    {
        logger.debug("Insert comment: {}", jsonStr);
        CommentDTO commentDTO = getFormJSON(jsonStr, CommentDTO.class);
        try
        {
            Assert.hasLength(commentDTO.getInformationId(), "资讯id不能为空");
            Assert.hasLength(commentDTO.getComment(), "评论内容不能为空");
            Assert.hasLength(commentDTO.getUserName(), "评论人不能为空");
            Assert.hasLength(commentDTO.getCommentId(), "评论id不能为空");
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            commentService.reply(commentDTO, modify);
            return success(INSERT_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Invalid parameter: {}", jsonStr, e);
            throw new InformationException(AI_NO_USERNAME);
        }
        catch (Exception e)
        {
            logger.error("Insert comment error: {}", jsonStr, e);
            throw new InformationException(FIELD_AGAIN);
        }
    }

    /**
     * 删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String delete(@RequestBody String jsonStr)
    {
        logger.debug("Delete comment with user: {}", jsonStr);
        CommentDTO dto = getFormJSON(jsonStr, CommentDTO.class);
        if(StringUtils.isBlank(dto.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        commentService.delete(dto.getId(), modify);
        return success(DELETE_SUCCESS);
    }

    /**
     * 分页查询（客户端）
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/like", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String like(@RequestBody String jsonStr)
    {
        logger.debug("like this comment：{}", jsonStr);
        CommentQueryDTO commentDTO = getFormJSON(jsonStr, CommentQueryDTO.class);
        if(StringUtils.isBlank(commentDTO.getCommentId()))
        {
            return fail("123", "必填项未填");
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        commentService.like(commentDTO.getCommentId(), modify);
        return success(LIKE_SUCCESS);
    }

    /**
     * 分页查询（客户端）
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/popular", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String popular(@RequestBody String jsonStr)
    {
        logger.debug("Find popular comment：{}", jsonStr);
        CommentQueryDTO commentDTO = getFormJSON(jsonStr, CommentQueryDTO.class);
        if(StringUtils.isBlank(commentDTO.getInformationId()))
        {
            return fail("123", "必填项未填");
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        PageDTO<AppCommentVO> result = commentService.popular(commentDTO.getInformationId(), modify);
        return success(result);
    }

    /**
     * 分页查询（客户端）
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String page(@RequestBody String jsonStr)
    {
        logger.debug("Find comment page：{}", jsonStr);
        CommentQueryDTO commentDTO = getFormJSON(jsonStr, CommentQueryDTO.class);
        if(StringUtils.isBlank(commentDTO.getInformationId()))
        {
            return fail("123", "必填项未填");
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        PageRequest pageRequest = genPageRequest(commentDTO);
        commentDTO.setShowStatus(DEFAULT_STATUS);

        PageDTO<AppCommentVO> result = commentService.page(commentDTO, pageRequest, modify);
        return success(result);
    }

    // ------------------------------ 私有变量及方法 ---------------------------------/

    private static Logger logger = LoggerFactory.getLogger(CommentApiController.class);

    @Value("${default.information.node}")
    private String defaultNode;

    @Autowired
    private CommentApiService commentService;
}