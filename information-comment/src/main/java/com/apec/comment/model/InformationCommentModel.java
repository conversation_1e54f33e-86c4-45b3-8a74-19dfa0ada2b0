package com.apec.information.model;

import com.apec.comment.model.InformationCommentReplyModel;
import com.apec.framework.jpa.model.BaseModel;
import com.apec.information.base.InformationBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

/**
 * 资讯回复记录
 * @date 2018-09-20
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Document(collection = "information_comment_model")
public class InformationCommentModel extends InformationBaseModel
{
    /**
     * 组织编号
     */
    private String orgNo;

    /**
     * 编号
     */
    private String categoryNo;

    /**
     * 编号
     */
    private String informationId;

    /**
     * 资讯标题
     */
    private String informationTitle;

    /**
     * 评论的用户id
     */
    private String userId;

    /**
     * 评论的用户名
     */
    private String userName;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 评论内容
     */
    private String showStatus;

    /**
     * 可见回复列表
     */
    private List<String> showList;

    /**
     * 回复列表，需要顺序，所以用Maps.newConcurrentMap()
     */
    private LinkedHashMap<String, InformationCommentReplyModel> replyList;

    /**
     * 不可见列表
     */
    private List<String> deleteList;

    /**
     * 点赞用户列表
     */
    private Set<String> likeList = new HashSet<>();

    /**
     * 点赞用户列表
     */
    private Integer likeCount;
}