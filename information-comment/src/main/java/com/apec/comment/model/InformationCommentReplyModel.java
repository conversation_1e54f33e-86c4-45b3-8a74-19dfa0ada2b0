package com.apec.comment.model;

import com.apec.framework.common.enumtype.EnableFlag;
import lombok.Data;

import java.util.Date;

/**
 * 资讯回复记录
 * @date 2018-09-20
 * <AUTHOR>
 */
@Data
public class InformationCommentReplyModel
{
    private String id;

    /**
     * 评论的用户id
     */
    private String userId;

    /**
     * 评论的用户名
     */
    private String userName;

    /**
     * 被评论的评论id
     */
    private String toReplyId;

    /**
     * 被评论的用户id
     */
    private String toUserId;

    /**
     * 被评论的用户名
     */
    private String toUserName;

    /**
     * 评论内容
     */
    private String comment;

    /**
     * 是否展示
     */
    private String showStatus;

    private EnableFlag enableFlag = EnableFlag.Y;

    private String modify;

    private Date createDate;
}
