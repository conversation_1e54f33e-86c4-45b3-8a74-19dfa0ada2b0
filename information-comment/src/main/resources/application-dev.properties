logging.level.com.apec=DEBUG
workerId=6

eureka.client.serviceUrl.defaultZone=http://*************:1111/eureka/

# mysql setting
spring.datasource.primary.url=*************************************************************************************************************************
spring.datasource.primary.username=root
spring.datasource.primary.password=Crm_2018

# redis setting
redis.database=6
redis.host=*************
redis.port=9001
redis.password=foobared
redis.timeout=5000
redis.pool.max-active=8
redis.pool.max-wait=-1
redis.pool.max-idle=8
redis.pool.min-idle=0

spring.redis.host=*************
spring.redis.port=9001
spring.redis.host2=*************
spring.redis.port2=9002
spring.redis.timeout=5000
spring.redis.maxRedirections=3000
spring.redis.password=foobared

# rabbit mq setting
spring.rabbitmq.host=*************
spring.rabbitmq.port=9001
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

# mongodb setting
spring.data.mongodb.uri=mongodb://*************:27017/information

# mt api
mt.api.baseurl=http://**************:16002