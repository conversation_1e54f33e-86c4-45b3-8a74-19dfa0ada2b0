package com.apec;

import com.apec.framework.base.BaseApplication;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.mq.Sender;
import com.apec.framework.springcloud.SpringCloudConfig;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 * @DESC: pages
 */
@EnableDiscoveryClient
@SpringBootApplication
@Import(value = SpringCloudConfig.class)
public class InformationApplication extends BaseApplication
{
    public static void main(String[] args)
    {
        new SpringApplicationBuilder(InformationApplication.class).web(true).run(args);
    }

    /**
     * 访问量的队列
     * @return
     */
    @Bean
    public Sender sender()
    {
        return new Sender();
    }

    @Bean
    public JedisCluster jedisCluster(@Value("${spring.redis.host}") String host,
        @Value("${spring.redis.port}") int port, @Value("${spring.redis.host2}") String host2,
        @Value("${spring.redis.port2}") int port2, @Value("${spring.redis.timeout}") int timeout,
        @Value("${spring.redis.maxRedirections}") int maxRedirections)
    {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxIdle(8);
        config.setMaxIdle(8);
        config.setMaxWaitMillis(-1L);
        config.setTestOnBorrow(false);
        config.setTestWhileIdle(false);
        Set<HostAndPort> jedisClusterNodes = new HashSet();
        jedisClusterNodes.add(new HostAndPort(host, port));
        jedisClusterNodes.add(new HostAndPort(host2, port2));
        JedisCluster jedisCluster = new JedisCluster(jedisClusterNodes, timeout, maxRedirections, config);
        return jedisCluster;
    }
}