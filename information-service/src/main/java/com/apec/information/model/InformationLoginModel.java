package com.apec.information.model;

import com.apec.framework.jpa.model.BaseModel;
import com.apec.information.util.ArrayDateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 资讯有状态记录
 * @date 2018-09-13
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Document(collection = "information_login_model")
public class InformationLoginModel extends BaseModel<String>
{
    /**
     * 组织编号
     */
    private String orgNo;

    /**
     * id
     */
    private String categoryNo;

    /**
     * id
     */
    private String informationId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 点赞
     */
    private Boolean like;

    /**
     * 点赞时间
     */
    private Date likeDate;

    /**
     * 点赞时间
     */
    private String likeDateString;

    /**
     * 收藏
     */
    private Boolean collect;

    /**
     * 收藏时间
     */
    private Date collectDate;

    /**
     * 收藏时间
     */
    private String collectDateString;

    public void setLikeDate(Date likeDate)
    {
        this.likeDate = likeDate;
        this.likeDateString = ArrayDateUtil.ymdToString(likeDate);
    }

    public void setCollectDate(Date collectDate)
    {
        this.collectDate = collectDate;
        this.collectDateString = ArrayDateUtil.ymdToString(collectDate);
    }
}