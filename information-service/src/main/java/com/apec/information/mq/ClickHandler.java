package com.apec.information.mq;

import com.apec.framework.common.enumtype.MqHandlerResult;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.mq.BaseClientHandler;
import com.apec.information.dto.BaseId;
import com.apec.information.model.InformationModel;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * mq接收处理
 * @date 2018-01-17 14:25
 * <AUTHOR>
 */
@Component
public class ClickHandler extends BaseClientHandler
{
    @RabbitListener(queues = "#{T(com.apec.information.mq.InfoRoutingKey).INFO_CLICK.getKeyName()}", containerFactory = "rabbitListenerContainerFactoryWithManual")
    void doMessage(Message message, @Header(org.springframework.amqp.support.AmqpHeaders.CHANNEL) Channel channel)
    {
        onMessage(message, channel);
    }

    @Override
    public MqHandlerResult handleMessage(Message message)
    {
        try
        {
            String json = new String(message.getBody(), "UTF-8");
            logger.info("点击详情：{}", json);
            BaseId baseId = JsonUtils.parseObject(json, BaseId.class);
            InformationModel information = mongoTemplate.findById(baseId.getId(), InformationModel.class);
            Long pv = information.getInformationPageView();
            pv = null == pv ? ONE : pv + 1;
            Update update = new Update();
            update.set("informationPageView", pv);
            mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(information.getId())), update,
                                      InformationModel.class);
            return MqHandlerResult.SUCCESS;
        }
        catch (Exception e)
        {
            logger.error("喜加一出错", e);
            return reHandler(message);
        }
    }

    private Logger logger = LoggerFactory.getLogger(getClass());

    private static final Long ONE = 1L;

    @Autowired
    private MongoTemplate mongoTemplate;
}
