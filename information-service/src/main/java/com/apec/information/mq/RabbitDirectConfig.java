package com.apec.information.mq;

import com.apec.framework.common.enumtype.MqRoutingKey;
import com.apec.framework.mq.BindingComponent;
import com.apec.framework.mq.routing.RoutingAmqpConfig;
import com.apec.framework.mq.routing.RoutingRecevierConfig;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import static com.apec.information.mq.InfoExchange.DEFAULT_EXCHANGE;
import static com.apec.information.mq.InfoRoutingKey.INFO_CLICK;

/**
 * mq的配置类
 * @date 2018-01-17 14:15
 * <AUTHOR>
 */
@Configuration
@Import(
    {RoutingAmqpConfig.class, RoutingRecevierConfig.class})
public class RabbitDirectConfig
{
    private Logger logger = LoggerFactory.getLogger(getClass());

    public RabbitDirectConfig()
    {
        RoutingAmqpConfig.setMqExchange(DEFAULT_EXCHANGE);
        MqRoutingKey[] array = ArrayUtils.toArray(INFO_CLICK);
        RoutingRecevierConfig.setKeyArray(array);
        BindingComponent click = new BindingComponent(DEFAULT_EXCHANGE.getExchangeName(),
                                                      INFO_CLICK.getKeyName(),
                                                      INFO_CLICK.getKeyName());
        BindingComponent[] bindingComponents = ArrayUtils.toArray(click);
        RoutingRecevierConfig.setBindingComponents(bindingComponents);

        logger.debug("实例化RabbitDirectConfig");
    }
}