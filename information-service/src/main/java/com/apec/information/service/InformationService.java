/*
 *
 *   @copyright      Copyright © 2017 - 2018. [深圳市中农易讯信息技术有限公司] All rights reserved.
 *   @project        APEC_CJ212_pages
 *   <AUTHOR>
 *   @date           2017-09-16 14:04:57
 *
 *   @lastModifie  2017-09-16 14:04:57
 *
 */

package com.apec.information.service;

import com.apec.framework.common.dto.PageDTO;
import com.apec.information.dto.InformationDTO;
import com.apec.information.dto.PageInformationDTO;
import com.apec.information.vo.InformationVO;
import org.springframework.data.domain.PageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 *
 * @DESC: InformationService
 */

public interface InformationService
{
    /**
     * 保存
     * @param informationDTO
     * @param modify
     */
    void insert(InformationDTO informationDTO, String modify);

    /**
     * 删除
     * @param id
     * @param modify
     */
    void delete(String id, String modify);

    /**
     * 修改
     * @param informationDTO
     * @param modify
     */
    void update(InformationDTO informationDTO, String modify);

    /**
     * 修改标签
     * @param informationDTO
     * @param modify
     */
    void changeLabel(InformationDTO informationDTO, String modify);

    /**
     * 修改状态
     * @param informationDTO
     * @param modify
     */
    void changePublicStatus(InformationDTO informationDTO, String modify);

    /**
     * 根据id查询
     * @param id
     * @param reader
     * @return
     */
    InformationVO findById(String id);

    /**
     * 根据id查询
     * @param id
     * @param reader
     * @return
     */
    InformationVO detail(String id, String reader);

    /**
     * 分页查询
     * @param informationDTO
     * @param request
     * @return
     */
    PageDTO<InformationVO> page(PageInformationDTO informationDTO, PageRequest request);

    /**
     * 中农日报资讯查询
     * @param articleType 文章类型
     * @return 中农日报资讯列表
     */
    List<InformationVO> indexList(String articleType);

    /**
     * 如果上传了base64的图片,则保存到阿里云并整理文章信息
     * @param informationDTO 资讯信息
     * @return base64转换阿里云oss后的资讯信息
     */
    InformationDTO saveBase64ImageInfo(InformationDTO informationDTO);
}