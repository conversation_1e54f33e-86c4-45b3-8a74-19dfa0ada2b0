//package com.apec.information.service.impl;
//
//import com.apec.framework.common.util.BeanUtils;
//import com.apec.information.dao.ClickDAO;
//import com.apec.information.dto.PageViewDTO;
//import com.apec.information.handler.InformationException;
//import com.apec.information.model.InformationClickModel;
//import com.apec.information.model.InformationClickModels;
//import com.apec.information.service.InformationClickService;
//import com.apec.information.service.InformationService;
//import com.apec.information.util.SnowFlakeKeyGen;
//import com.apec.information.vo.InformationVO;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.stereotype.Service;
//
//import java.util.Date;
//
///**
// * <AUTHOR>
// * @date 2017-09-16 14:04:57
// */
//@Service
//public class InformationClickServiceImpl implements InformationClickService
//{
//    private Logger LOG = LoggerFactory.getLogger(getClass());
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private SnowFlakeKeyGen idGen;
//
//    @Autowired
//    private InformationService informationService;
//
//    @Autowired
//    private ClickDAO clickDAO;
//
//    @Override
//    public void click(PageViewDTO pageViewDTO)
//    {
//        InformationVO information = informationService.findById(pageViewDTO.getId());
//        if(null == information)
//        {
//            throw new InformationException("25013001");
//        }
//        InformationClickModel clickModel = new InformationClickModel();
//        Date now = new Date();
//        clickModel.setId(Long.toString(idGen.nextId()));
//        clickModel.setCreateBy(pageViewDTO.getBrowserUuid());
//        clickModel.setLastUpdateBy(pageViewDTO.getBrowserUuid());
//        clickModel.setCreateDate(now);
//        clickModel.setLastUpdateDate(now);
//
//        // 插入前端提供信息
//        clickModel.setInformationId(pageViewDTO.getId());
//        clickModel.setBrowserUuid(pageViewDTO.getBrowserUuid());
//        clickModel.setUserId(pageViewDTO.getUserId());
//
//        // 插入资讯部分信息
//        clickModel.setOrgNo(information.getOrgNo());
//        clickModel.setCategoryNo(information.getCategoryNo());
//        mongoTemplate.save(clickModel);
//        InformationClickModels clickModels = new InformationClickModels();
//        BeanUtils.copyPropertiesIgnoreNullFilds(clickModel, clickModels);
//        clickDAO.save(clickModels);
//    }
//}