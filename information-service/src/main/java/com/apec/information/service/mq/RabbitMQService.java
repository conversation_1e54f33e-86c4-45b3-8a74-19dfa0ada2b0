//package com.apec.information.service.mq;
//
//import com.apec.information.model.InformationModel;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.data.mongodb.core.query.Update;
//import org.springframework.stereotype.Service;
//
//
//
///**
// * <AUTHOR>
// * @description MQ 的消息
// * @date 2018/7/25 17:57
// **/
//@Service
//public class RabbitMQService {
//
//    public static final Logger LOGGER = LoggerFactory.getLogger(RabbitMQService.class);
//
//    @Autowired
//    private RabbitTemplate rabbitTemplate;
//    @Value("${mq.cms.rich.text.view.count}")
//    private String bindingKey;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    public void sendCmsRichText(InformationModel InformationModel) {
//        LOGGER.info("---生产消息准备中;{}", InformationModel);
//        try {
//            String content = objectMapper.writeValueAsString(InformationModel);
//            rabbitTemplate.convertAndSend("", bindingKey, content);
//        } catch (JsonProcessingException e) {
//            LOGGER.error("-----生产失败：", e);
//        }
//        LOGGER.info("-----生产已发送");
//
//    }
//
//    @RabbitListener(queues = {"${mq.cms.rich.text.view.count}"})
//    public void receiveCmsRichText(Message content) {
//        LOGGER.info("----开始消费消息：{}", content);
//        try {
//            String message = new String(content.getBody(), "UTF-8");
//            InformationModel InformationModel = objectMapper.readValue(message, InformationModel.class);
//            Update update = new Update();
//            update.set("viewCount", InformationModel.getViewCount());
//            mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(InformationModel.getId())), update, InformationModel.class);
//        } catch (Exception e) {
//            LOGGER.error("-----消费失败：", e);
//        }
//        LOGGER.info("---------消费成功：{}", content);
//    }
//
//    public String getBindingKey() {
//        return bindingKey;
//    }
//
//    public void setBindingKey(String bindingKey) {
//        this.bindingKey = bindingKey;
//    }
//
//}
