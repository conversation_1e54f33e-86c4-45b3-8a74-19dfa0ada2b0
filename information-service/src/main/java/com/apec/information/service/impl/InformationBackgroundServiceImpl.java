package com.apec.information.service.impl;

import com.apec.framework.cache.CacheService;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.constant.InformationConstants;
import com.apec.information.dto.SaveInformationIndexDTO;
import com.apec.information.service.InformationBackgroundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/3/7
 */
@Service
public class InformationBackgroundServiceImpl implements InformationBackgroundService {


    @Autowired
    private CacheService cacheService;

    /**
     * 保存'每日资讯', '咨询精选'列表(默认放到首位)
     * @param dto 资讯id数组 + 资讯类型
     * @return 处理结果
     */
    @Override
    public String saveIndexArticle(SaveInformationIndexDTO dto) {
        String key = InformationConstants.CACHE_INFORMATION_INDEX_ARTICLE + dto.getArticleType();
        cacheService.add(key, JsonUtils.toJSONString(dto.getInformationIds()));
        return InformationConstants.UPDATE_SUCCESS;
    }

}
