package com.apec.information.service.impl;

import com.apec.information.model.InformationModel;
import com.apec.information.service.InformationLoginService;
import com.apec.information.util.ArrayDateUtil;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2017-09-16 14:04:57
 */
@Service
public class InformationLoginServiceImpl implements InformationLoginService
{
    private Logger LOG = LoggerFactory.getLogger(getClass());

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void like(String id, String modify)
    {
        InformationModel info = mongoTemplate.findById(id, InformationModel.class);
        info.setLikeList(add(info.getLikeList(), modify));
        info.setLastInteractTimestamp(System.currentTimeMillis());
        mongoTemplate.save(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelLike(String id, String modify)
    {
        InformationModel info = mongoTemplate.findById(id, InformationModel.class);
        info.setLikeList(remove(info.getLikeList(), modify));
        info.setLastInteractTimestamp(System.currentTimeMillis());
        mongoTemplate.save(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void collect(String id, String modify)
    {
        InformationModel info = mongoTemplate.findById(id, InformationModel.class);
        info.setCollectList(add(info.getCollectList(), modify));
        info.setLastInteractTimestamp(System.currentTimeMillis());
        mongoTemplate.save(info);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelCollect(String id, String modify)
    {
        InformationModel info = mongoTemplate.findById(id, InformationModel.class);
        info.setCollectList(remove(info.getCollectList(), modify));
        info.setLastInteractTimestamp(System.currentTimeMillis());
        mongoTemplate.save(info);
    }

    private Set<String> add(Set<String> set, String modify)
    {
        if(null == set)
        {
            set = Sets.newHashSet();
        }
        set.add(modify);
        return set;
    }

    private Set<String> remove(Set<String> set, String modify)
    {
        if(null == set)
        {
            return Sets.newHashSet();
        }
        set.remove(modify);
        return set;
    }
}