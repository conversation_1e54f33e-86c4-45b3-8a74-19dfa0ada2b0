package com.apec.information.service.impl;

import com.alibaba.fastjson.JSON;
import com.apec.framework.cache.CacheService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.exception.ApecRuntimeException;
import com.apec.framework.common.model.ResultData;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.framework.mq.Sender;
import com.apec.framework.springcloud.SpringCloudClient;
import com.apec.information.dto.*;
import com.apec.information.enumtype.LinkTypeEnum;
import com.apec.information.handler.InformationException;
import com.apec.information.model.InformationModel;
import com.apec.information.service.InformationService;
import com.apec.information.util.SnowFlakeKeyGen;
import com.apec.information.vo.CategoryVO;
import com.apec.information.vo.ImageUpload2ThirdPartyVO;
import com.apec.information.vo.InformationDetailVO;
import com.apec.information.vo.InformationVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.apec.information.constant.InformationConstants.*;
import static com.apec.information.mq.InfoExchange.DEFAULT_EXCHANGE;
import static com.apec.information.mq.InfoRoutingKey.INFO_CLICK;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

/**
 * <AUTHOR>
 * @date 2017-09-16 14:04:57
 */
@Service
public class InformationServiceImpl implements InformationService
{
    private Logger LOG = LoggerFactory.getLogger(getClass());

    private static final String[] IGNORE_FIELDS = ArrayUtils.toArray("id", "createDate", "informationNo");

    @Value("${session.time.out}")
    private Integer sessionTime;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private NamedParameterJdbcTemplate namedTemplate;

    @Autowired
    private SnowFlakeKeyGen idGen;

    @Autowired
    private Sender sender;

    @Autowired
    private CacheService cacheService;

    @Value("${category.detail}")
    private String categoryDetail;

    @Value("${third-party-service.uploadImage}")
    private String uploadImage;

    @Autowired
    private SpringCloudClient springCloudClient;

    private final static String COMMENT_COUNT = "SELECT a.INFORMATION_ID,a.COMMENT_COUNT FROM information_info a,(SELECT INFORMATION_ID,MAX(STATS_DATE) 'STATS_DATE' FROM information_info WHERE INFORMATION_ID IN (:idList)) b WHERE a.`INFORMATION_ID`=b.`INFORMATION_ID` AND a.`STATS_DATE`=b.`STATS_DATE`";

    @Override
    public void insert(InformationDTO informationDTO, String modify)
    {
        InformationModel information = new InformationModel();
        BeanUtils.copyPropertiesIgnoreNullFilds(informationDTO, information);
        information.setInformationSource(LinkTypeEnum.INNER.val());
        information.setInformationSourceName(informationDTO.getOrgName());
        information.setInformationEditable(TRUE);
        information.setId(Long.toString(idGen.nextId()));
        information.setInformationNo(information.getId());
        information.setCreateBy(modify);
        information.setLastUpdateBy(modify);
        Date now = new Date();
        information.setCreateDate(now);
        information.setLastUpdateDate(now);
        information.setLastInteractTimestamp(now.getTime());
        if(CollectionUtils.isEmpty(informationDTO.getCategoryNos())){
            information.setCategoryNo(informationDTO.getCategoryNo());
            information.setCategoryNos(Lists.newArrayList(informationDTO.getCategoryNo()));
        }else{
            information.setCategoryNo(StringUtils.join(informationDTO.getCategoryNos(), ","));
            information.setCategoryNos(informationDTO.getCategoryNos());

        }
        StringBuilder stringBuilder = new StringBuilder();
        information.getCategoryNos().forEach(f->{
            CategoryDTO categoryDTO = new CategoryDTO();
            categoryDTO.setCategoryNo(f);
            List<CategoryVO> categoryVOS = getCategoryNo(categoryDTO);
            if(!CollectionUtils.isEmpty(categoryVOS)) {
                stringBuilder.append(categoryVOS.get(0).getCategoryName()).append("/");
            }
        });
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        information.setCategoryName(stringBuilder.toString());
        mongoTemplate.save(information);
    }

    @Override
    public void delete(String id, String modify)
    {
        InformationModel information = mongoTemplate.findById(id, InformationModel.class);
        information.setEnableFlag(EnableFlag.N);
        information.setLastUpdateBy(modify);
        Date now = new Date();
        information.setLastUpdateDate(now);
        information.setLastInteractTimestamp(now.getTime());
        mongoTemplate.save(information);
    }

    @Override
    public void update(InformationDTO dto, String modify)
    {
        InformationModel information = mongoTemplate.findById(dto.getId(), InformationModel.class);
        if(FALSE.equals(information.getInformationEditable()))
        {
            throw new InformationException(CANNOT_EDIT);
        }
        BeanUtils.copyPropertiesIgnoreNullFilds(dto, information, IGNORE_FIELDS);
        information.setEnableFlag(EnableFlag.Y);
        information.setLastUpdateBy(modify);
        Date now = new Date();
        information.setLastUpdateDate(now);
        information.setLastInteractTimestamp(now.getTime());
        if(CollectionUtils.isEmpty(dto.getCategoryNos())){
            information.setCategoryNo(dto.getCategoryNo());
            information.setCategoryNos(Lists.newArrayList(dto.getCategoryNo()));
        }else{
            information.setCategoryNo(StringUtils.join(dto.getCategoryNos(), ","));
            information.setCategoryNos(dto.getCategoryNos());

        }
        StringBuilder stringBuilder = new StringBuilder();
        information.getCategoryNos().forEach(f->{
            CategoryDTO categoryDTO = new CategoryDTO();
            categoryDTO.setCategoryNo(f);
            List<CategoryVO> categoryVOS = getCategoryNo(categoryDTO);
            if(!CollectionUtils.isEmpty(categoryVOS)) {
                stringBuilder.append(categoryVOS.get(0).getCategoryName()).append("/");
            }
        });
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        information.setCategoryName(stringBuilder.toString());
        mongoTemplate.save(information);
        String key = CACHE_INFORMATION_DETAIL + dto.getId();
        cacheService.remove(key);
    }

    @Override
    public void changeLabel(InformationDTO informationDTO, String modify)
    {
        InformationModel information = mongoTemplate.findById(informationDTO.getId(), InformationModel.class);
        information.setInformationLabel(informationDTO.getInformationLabel());
        information.setLastUpdateBy(modify);
        Date now = new Date();
        information.setLastUpdateDate(now);
        information.setLastInteractTimestamp(now.getTime());
        mongoTemplate.save(information);
        String key = CACHE_INFORMATION_DETAIL + informationDTO.getId();
        cacheService.remove(key);
    }

    @Override
    public void changePublicStatus(InformationDTO informationDTO, String modify)
    {
        InformationModel information = mongoTemplate.findById(informationDTO.getId(), InformationModel.class);
        information.setInformationPublicStatus(informationDTO.getInformationPublicStatus());
        information.setLastUpdateBy(modify);
        Date now = new Date();
        information.setLastUpdateDate(now);
        information.setLastInteractTimestamp(now.getTime());
        mongoTemplate.save(information);
        String key = CACHE_INFORMATION_DETAIL + informationDTO.getId();
        cacheService.remove(key);
    }

    @Override
    public InformationVO findById(String id)
    {
        InformationModel information = mongoTemplate.findById(id, InformationModel.class);
        if(null == information)
        {
            throw new InformationException("25013001");
        }
        InformationDetailVO result = new InformationDetailVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(information, result);
        if(StringUtils.isNotBlank(information.getCategoryNo())){
            result.setCategoryNos(Arrays.asList(information.getCategoryNo().split(",")));
        }else{
            result.setCategoryNos(new ArrayList<>());
        }
        return result;
    }

    @Override
    public InformationDetailVO detail(String id, String reader)
    {
        BaseId baseId = new BaseId();
        baseId.setId(id);
        sender.send(DEFAULT_EXCHANGE.getExchangeName(),
                    INFO_CLICK.getKeyName(),
                    JsonUtils.toJSONString(baseId));
        InformationDetailVO result = new InformationDetailVO();
        InformationModel information = mongoTemplate.findById(id, InformationModel.class);
        if(null == information)
        {
            throw new InformationException("25013001");
        }
        BeanUtils.copyPropertiesIgnoreNullFilds(information, result);
        Set<String> collectList = information.getCollectList();
        Set<String> likeList = information.getLikeList();
        if(StringUtils.isNotBlank(information.getCategoryNo())){
            result.setCategoryNos(Arrays.asList(information.getCategoryNo().split(",")));
        }else{
            result.setCategoryNos(new ArrayList<>());
        }
        result.setInformationCollectCount(null == collectList ? 0 : collectList.size());
        result.setInformationLikeCount(null == likeList ? 0 : likeList.size());

        //获取是否点赞与是否收藏
        if(StringUtils.isNotBlank(reader))
        {
            result.setLike(null != likeList && likeList.contains(reader));
            result.setCollect(null != collectList && collectList.contains(reader));
        }

        List<String> list = initList(result.getInformationImageList(), result.getInformationCoverImage());
        result.setInformationImageList(list);
        return result;
    }

    @Override
    public PageDTO<InformationVO> page(PageInformationDTO dto, PageRequest request)
    {
        PageDTO<InformationVO> result = new PageDTO<>();
        List<InformationVO> dtoList = Lists.newArrayList();
        Query query = filterCondition(dto);
        Long count = mongoTemplate.count(query, InformationModel.class);
        int startRow = dto.getPageSize() * (dto.getPageNumber() - 1);
        Query createDate = query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createDate")));
        LOG.info("mongodb sql:{}", query.toString());
        List<InformationModel> page = mongoTemplate
            .find(createDate.skip(startRow).limit(dto.getPageSize()), InformationModel.class);

//        Query query2 = filterCondition2(dto);
//        Long count2 = mongoTemplate.count(query2, InformationModel.class);
//        Query createDate2 = query2.with(new Sort(new Sort.Order(Sort.Direction.DESC, "createDate")));
//        LOG.info("mongodb sql:{}", query.toString());
//        List<InformationModel> page3 = mongoTemplate.find(createDate2.skip(startRow).limit(dto.getPageSize()), InformationModel.class);
//        page2.addAll(page3);
//        List<InformationModel> page = page2.stream().sorted(Comparator.comparing(InformationModel::getCreateDate).reversed()).distinct().limit(10).collect(Collectors.toList());
//        count = count > count2 ? count : count2;

        if(page.size() < 1)
        {
            result.setTotalElements(0);
            result.setNumber(0);
            result.setTotalPages(0);
            return result;
        }
        InformationVO target;
        List<String> idList = Lists.newArrayListWithCapacity(page.size());
        page.forEach(im -> idList.add(im.getId()));
        Map<String, Object> map = Maps.newHashMap();
        map.put("idList", idList);
        Map<String, Integer> maps = Maps.newHashMap();
        namedTemplate.query(COMMENT_COUNT, map, rs -> {
            maps.put(rs.getString("INFORMATION_ID"), rs.getInt("COMMENT_COUNT"));
        });
        List<CategoryVO> categoryVOS = getCategoryNo(new CategoryDTO());
        Map<String, String> categoryMap = categoryVOS.stream().collect(Collectors.toMap(CategoryVO::getCategoryNo, CategoryVO::getCategoryName, (key1, key2) -> key2));

        for(InformationModel o : page)
        {
            target = new InformationVO();
            BeanUtils.copyPropertiesIgnoreNullFilds(o, target, "informationContent");
            if(StringUtils.isNotBlank(o.getCategoryNo())){
                List<String> categoryList = Arrays.asList(o.getCategoryNo().split(","));
                StringBuilder builder = new StringBuilder();
                for(String category : categoryList){
                    if(categoryMap.containsKey(category)){
                        builder.append(categoryMap.get(category) + "/");
                    }
                }
                if(builder.length() > 0 ){
                    builder.deleteCharAt(builder.length() - 1);
                    target.setCategoryName(builder.toString());
                }
                target.setCategoryNos(categoryList);
            }else{
                target.setCategoryNos(new ArrayList<>());
            }
            target.setInformationCommentCount(maps.get(o.getId()));
            dtoList.add(target);
        }
        result.build(count, request, dtoList);
        return result;
    }

    /**
     * 中农日报资讯查询
     * @param articleType 文章类型
     * @return 中农日报资讯列表
     */
    @Override
    public List<InformationVO> indexList(String articleType) {
        String key = CACHE_INFORMATION_INDEX_ARTICLE + articleType;
        String value = cacheService.get(key);
        if(StringUtils.isNotBlank(value)) {
            List<String> informationIds = JsonUtils.parseArray(value, String.class);
            Query query = Query.query(Criteria.where("enableFlag").is(EnableFlag.Y));
            query.addCriteria(Criteria.where("informationPublicStatus").is(DEFAULT_STATUS));
            query.addCriteria(Criteria.where("id").in(informationIds));
            List<InformationModel> informationModelList = mongoTemplate.find(query, InformationModel.class);
            if (CollectionUtils.isEmpty(informationModelList)) {
                return new ArrayList<>();
            }
            List<CategoryVO> categoryVOS = getCategoryNo(new CategoryDTO());
            Map<String, String> categoryMap = categoryVOS.stream().collect(Collectors.toMap(CategoryVO::getCategoryNo, CategoryVO::getCategoryName,(key1, key2) -> key2));
            Map<String, InformationModel> informationMap = informationModelList.stream().collect(Collectors.toMap(InformationModel::getId, Function.identity(), (key1, key2) -> key2));
            List<InformationVO> dtoList = Lists.newArrayList();
            for (String id : informationIds) {
                InformationModel informationModel = informationMap.get(id);
                if(Objects.nonNull(informationModel)){
                    InformationVO target = new InformationVO();
                    BeanUtils.copyPropertiesIgnoreNullFilds(informationModel, target);
                    List<String> list = initList(target.getInformationImageList(), target.getInformationCoverImage());
                    target.setInformationImageList(list);
                    if(StringUtils.isNotBlank(informationModel.getCategoryNo())){
                        List<String> categoryList = Arrays.asList(informationModel.getCategoryNo().split(","));
                        StringBuilder builder = new StringBuilder();
                        for(String category : categoryList){
                            if(categoryMap.containsKey(category)){
                                builder.append(categoryMap.get(category)).append("/");
                            }
                        }
                        if(builder.length() > 0 ){
                            builder.deleteCharAt(builder.length() - 1);
                            target.setCategoryName(builder.toString());
                        }
                        target.setCategoryNos(categoryList);
                    }else{
                        target.setCategoryNos(new ArrayList<>());
                    }

                    dtoList.add(target);
                }

            }
            return dtoList;
        }

        return new ArrayList<>();
    }

    private List<CategoryVO> getCategoryNo(CategoryDTO categoryDTO){
        ResultData resultData = springCloudClient.restfulPost(categoryDetail, JsonUtils.toJSONString(categoryDTO));
        if (!resultData.isSucceed()) {
            throw new ApecRuntimeException("-1", "类目服务查询详情失败");
        }
        List<CategoryVO> categoryVOS = JsonUtils.parseArray(JsonUtils.toJSONString(resultData.getData()), CategoryVO.class);
        return categoryVOS;
    }
    /**
     * 如果上传了base64的图片,则保存到阿里云并整理文章信息
     *
     * @param informationDTO 资讯信息
     * @return base64转换阿里云oss后的资讯信息
     */
    @Override
    public InformationDTO saveBase64ImageInfo(InformationDTO informationDTO) {
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(informationDTO.getInformationImageList())){
            ArrayList<String> imageUrlList = new ArrayList<>();
            String informationContent = informationDTO.getInformationContent();
            for(String imageUrl: informationDTO.getInformationImageList()){
                if(imageUrl.startsWith("data:image/")){
                    // 上传图片后返回数据图片地址
                    String type = imageUrl.split(";base64")[0];
                    type = type.split("/")[1];
                    ImageUpload2ThirdPartyDTO uploadDTO = new ImageUpload2ThirdPartyDTO(type, imageUrl);
                    ResultData resultData = springCloudClient.restfulPost(uploadImage, JsonUtils.toJSONString(uploadDTO));
                    if (!resultData.isSucceed()) {
                        throw new ApecRuntimeException("-1", "上传图片失败,请重新尝试");
                    }
                    ImageUpload2ThirdPartyVO imageInfo = JsonUtils.parseObject(JsonUtils.toJSONString(resultData.getData()), ImageUpload2ThirdPartyVO.class);
                    if(Objects.isNull(imageInfo) || StringUtils.isBlank(imageInfo.getUrl())){
                        throw new ApecRuntimeException("-1", "上传图片失败,请重新尝试");
                    }
                    // 默认上传完的图片是带水印的,所以我这取有效地址就好
                    String newImageUrl = imageInfo.getUrl().substring(0, imageInfo.getUrl().lastIndexOf("?"));
                    // 替换图片
                    informationContent = informationContent.replace(imageUrl, newImageUrl);
                    imageUrlList.add(newImageUrl);
                    if(informationDTO.getInformationCoverImage().equals(imageUrl)){
                        informationDTO.setInformationCoverImage(newImageUrl);
                    }
                }else{
                    imageUrlList.add(imageUrl);
                }
            }
            informationDTO.setInformationContent(informationContent);
            informationDTO.setInformationImageList(imageUrlList);
        }
        return informationDTO;
    }

    /**
     * mongo 的过滤条件
     * @param dto
     * @return
     */
    private Query filterCondition(PageInformationDTO dto) {
        Query query = Query.query(Criteria.where("enableFlag").is(EnableFlag.Y));

        if (StringUtils.isNotBlank(dto.getId())) {
            query = query.addCriteria(Criteria.where("id").is(dto.getId()));
        }
        if (StringUtils.isNotBlank(dto.getInformationPublicStatus())) {
            query = query.addCriteria(Criteria.where("informationPublicStatus").is(dto.getInformationPublicStatus()));
        }
        if (StringUtils.isNotBlank(dto.getCategoryNo())) {
            Criteria criteria = new Criteria();
            Pattern pattern = Pattern.compile("^.*" + dto.getCategoryNo() + "_.*$", Pattern.CASE_INSENSITIVE);
            criteria.orOperator(Criteria.where("categoryNo").regex(dto.getCategoryNo()), Criteria.where("categoryNo").regex(pattern));
            query.addCriteria(criteria);
        }
        if (StringUtils.isNotBlank(dto.getInformationTitle())) {
            Pattern pattern = Pattern.compile("^.*" + dto.getInformationTitle() + ".*$", Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("informationTitle").regex(pattern));
        }
        if (StringUtils.isNotBlank(dto.getInformationSummary())) {
            Pattern pattern = Pattern.compile("^.*" + dto.getInformationSummary() + ".*$", Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("informationSummary").regex(pattern));
        }
        if (!CollectionUtils.isEmpty(dto.getInformationLabel())) {
            for (String label : dto.getInformationLabel()) {
                query.addCriteria(Criteria.where("informationLabel").is(label));
            }
        }
        if (null != dto.getExistImage()) {
            if (dto.getExistImage()) {
                query.addCriteria(Criteria.where("informationImageList.0").exists(true));
            } else {
                query.addCriteria(Criteria.where("informationImageList.0").exists(false));
            }
        }
        if (StringUtils.isNotBlank(dto.getInformationSource())) {
            query.addCriteria(Criteria.where("informationSource").is(dto.getInformationSource()));
        }
        if (StringUtils.isNotBlank(dto.getInformationSourceName())) {
            query.addCriteria(Criteria.where("informationSourceName").is(dto.getInformationSourceName()));
        }
        if (null != dto.getStartDate() && null != dto.getEndDate()) {
            query.addCriteria(Criteria.where("lastUpdateDate").gt(dto.getStartDate()).lt(dto.getEndDate()));
        }
        if (null != dto.getInformationModule()) {
            query.addCriteria(Criteria.where("informationModule").is(dto.getInformationModule()));
        }
        if (null != dto.getCollect() && dto.getCollect()) {
            query.addCriteria(Criteria.where("collectList").is(dto.getUserNo()));
        }
        if (StringUtils.isNotBlank(dto.getMerchantId())) {
            if (!"DF".equals(dto.getMerchantId())) {
                query = query.addCriteria(Criteria.where("merchantId").is(dto.getMerchantId()));
            }
        }
        if (StringUtils.isNotBlank(dto.getOrgNo()) && !ROOT_NODE.equals(dto.getOrgNo())) {
            query = query.addCriteria(Criteria.where("orgNo").is(dto.getOrgNo()));
        }
        LOG.info("-----mongo sql is:{} ", query.toString());
        return query;
    }

    private List<String> initList(List<String> imageList, String coverImage)
    {
        if(CollectionUtils.isEmpty(imageList))
        {
            List<String> result = Lists.newArrayListWithCapacity(5);
            if(StringUtils.isNotBlank(coverImage))
            {
                result.add(coverImage);
            }
            return result;
        }
        return imageList;
    }
}