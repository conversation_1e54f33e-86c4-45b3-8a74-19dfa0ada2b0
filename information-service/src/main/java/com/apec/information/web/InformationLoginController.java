package com.apec.information.web;

import com.apec.information.base.MyController;
import com.apec.information.dto.BaseId;
import com.apec.information.handler.InformationException;
import com.apec.information.service.InformationLoginService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.information.constant.InformationConstants.*;

/**
 * 放白名单的 控制器
 * <AUTHOR>
 * @date 2018/8/1 14:38
 **/
@RestController
@RequestMapping("/api")
public class InformationLoginController extends MyController
{

    private static Logger logger = LoggerFactory.getLogger(InformationLoginController.class);

    @Autowired
    private InformationLoginService informationService;

    /**
     * 用户点赞
     * @param jsonStr 用户的id，文章的Id
     * @return
     */
    @RequestMapping(value = "/like", method = RequestMethod.POST)
    public String like(@RequestBody String jsonStr)
    {
        logger.info("Like information: {} ", jsonStr);
        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("like must be picture!");
            throw new InformationException(LOGIN_NECESSARY_ERROR);
        }
        informationService.like(baseId.getId(), modify);
        return success(LIKE_SUCCESS);
    }

    /**
     * 用户取消点赞
     * @param jsonStr 用户的id，文章的Id
     * @return
     */
    @RequestMapping(value = "/cancelLike", method = RequestMethod.POST)
    public String cancelLike(@RequestBody String jsonStr)
    {
        logger.info("Cancel like information: {} ", jsonStr);
        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("like must be picture!");
            throw new InformationException(LOGIN_NECESSARY_ERROR);
        }
        informationService.cancelLike(baseId.getId(), modify);
        return success(CANCEL_LIKE_SUCCESS);
    }

    /**
     * 用户收藏
     * @param jsonStr 用户的id，文章的Id
     * @return
     */
    @RequestMapping(value = "/collect", method = RequestMethod.POST)
    public String collect(@RequestBody String jsonStr)
    {
        logger.info("Collect information: {} ", jsonStr);
        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("like must be picture!");
            throw new InformationException(LOGIN_NECESSARY_ERROR);
        }
        informationService.collect(baseId.getId(), modify);
        return success(COLLECT_SUCCESS);
    }

    /**
     * 用户取消收藏
     * @param jsonStr 用户的id，文章的Id
     * @return
     */
    @RequestMapping(value = "/cancelCollect", method = RequestMethod.POST)
    public String cancelCollect(@RequestBody String jsonStr)
    {
        logger.info("Cancel collect information: {} ", jsonStr);
        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("like must be picture!");
            throw new InformationException(LOGIN_NECESSARY_ERROR);
        }
        informationService.cancelCollect(baseId.getId(), modify);
        return success(CANCEL_COLLECT_SUCCESS);
    }
}