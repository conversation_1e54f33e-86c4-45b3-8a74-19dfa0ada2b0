package com.apec.information.web;

import com.apec.framework.cache.CacheService;
import com.apec.information.base.MyController;
import com.apec.information.dto.SaveInformationIndexDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.service.InformationBackgroundService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @date 2023-03-07
 */
@RestController
@RequestMapping(value = "/bgInformation")
public class InformationBackgroundController extends MyController {

    private static final Logger logger = LoggerFactory.getLogger(InformationBackgroundController.class);

    private  InformationBackgroundService informationBackgroundService;


    /**
     * 保存'每日资讯', '咨询精选'列表(默认放到首位)
     * @param jsonStr 资讯id数组 + 资讯类型
     * @return 操作结果
     */
    @RequestMapping(value = "/saveIndexArticle", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String saveIndexArticle(@RequestBody String jsonStr) {
        logger.debug("saveIndexArticle : {}", jsonStr);
        SaveInformationIndexDTO dto = getFormJSON(jsonStr, SaveInformationIndexDTO.class);
        if(StringUtils.isBlank(dto.getArticleType())){
            logger.error("Invalid parameter: articleType cannot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        if(CollectionUtils.isEmpty(dto.getInformationIds())){
            logger.error("Invalid parameter: informationIds cannot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String result = informationBackgroundService.saveIndexArticle(dto);
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }
    @Autowired
    public void setInformationBackgroundService(InformationBackgroundService informationBackgroundService) {
        this.informationBackgroundService = informationBackgroundService;
    }
}
