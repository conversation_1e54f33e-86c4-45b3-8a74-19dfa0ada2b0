//package com.apec.information.web;
//
//import com.apec.information.base.MyController;
//import com.apec.information.dto.PageViewDTO;
//import com.apec.information.service.InformationClickService;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
//import static com.apec.information.constant.InformationConstants.*;
//
///**
// * 放白名单的 控制器
// * <AUTHOR>
// * @date 2018/8/1 14:38
// **/
//@RestController
//@RequestMapping("/api")
//public class InformationClickController extends MyController
//{
//
//    private static Logger logger = LoggerFactory.getLogger(InformationClickController.class);
//
//    @Autowired
//    private InformationClickService informationService;
//
//    /**
//     * 用户点赞
//     * @param jsonStr 用户的id，文章的Id
//     * @return
//     */
//    @RequestMapping(value = "/click", method = RequestMethod.POST)
//    public String click(@RequestBody String jsonStr)
//    {
//        logger.info("Click information: {} ", jsonStr);
//        PageViewDTO pageViewDTO = getFormJSON(jsonStr, PageViewDTO.class);
//        if(StringUtils.isBlank(pageViewDTO.getId()))
//        {
//            logger.error("Invalid parameter: id is null!");
//            return success(NULL_STR);
//        }
//        informationService.click(pageViewDTO);
//        return success(NULL_STR);
//    }
//}