package com.apec.information.web;

import com.apec.framework.cache.CacheService;
import com.apec.framework.common.dto.PageDTO;
import com.apec.information.base.MyController;
import com.apec.information.dto.*;
import com.apec.information.handler.InformationException;
import com.apec.information.service.InformationService;
import com.apec.information.vo.InformationVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.apec.information.constant.InformationConstants.*;

/**
 * <AUTHOR>
 * @date 2018-09-19
 */
@RestController
@RefreshScope
@RequestMapping(value = "/information")
public class InformationController extends MyController {

    @Value("${third-party-service.uploadImage}")
    private String uploadImage;
    /**
     * 新增资讯
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/insert", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String insert(@RequestBody String jsonStr)
    {
        logger.debug("Insert information: {}", jsonStr);
        InformationDTO informationDTO = getFormJSON(jsonStr, InformationDTO.class);
        try
        {
            Assert.hasLength(informationDTO.getInformationContent(), "内容不能为空");
            Assert.hasLength(informationDTO.getOrgNo(), "组织编号不能为空");
            String modify = getUserNo(getPageJSON(jsonStr, Object.class));
            informationDTO = informationService.saveBase64ImageInfo(informationDTO);
            informationService.insert(informationDTO, modify);
            return success(INSERT_SUCCESS);
        }
        catch (IllegalArgumentException e)
        {
            logger.error("Invalid parameter: {}", jsonStr, e);
            throw new InformationException(FIELD_ILLEGAL);
        }
        catch (Exception e)
        {
            logger.error("Insert information error: {}", jsonStr, e);
            throw new InformationException(FIELD_AGAIN);
        }
    }

    /**
     * 删除
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String delete(@RequestBody String jsonStr)
    {
        logger.debug("Delete information: {}", jsonStr);
        CmsRichTextDTO dto = getFormJSON(jsonStr, CmsRichTextDTO.class);
        if(StringUtils.isBlank(dto.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        informationService.delete(dto.getId(), modify);
        return success(DELETE_SUCCESS);
    }

    /**
     * 修改
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String update(@RequestBody String jsonStr)
    {
        logger.debug("Update information: {}", jsonStr);
        InformationDTO informationDTO = getFormJSON(jsonStr, InformationDTO.class);
        if(StringUtils.isBlank(informationDTO.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        informationDTO = informationService.saveBase64ImageInfo(informationDTO);
        informationService.update(informationDTO, modify);
        return success(UPDATE_SUCCESS);
    }

    /**
     * 根据ID改页面状态：启用或未启用
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/changeLabel", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String changeLabel(@RequestBody String jsonStr)
    {
        logger.debug("Change information label：{}", jsonStr);
        InformationDTO informationDTO = getFormJSON(jsonStr, InformationDTO.class);
        if(StringUtils.isBlank(informationDTO.getId()))
        {
            logger.error("Invalid parameter: id or public status canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        informationService.changeLabel(informationDTO, modify);
        return success(UPDATE_SUCCESS);
    }

    /**
     * 根据ID改页面状态：启用或未启用
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/changePublicStatus", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String changePublicStatus(@RequestBody String jsonStr)
    {
        logger.debug("Change information public status：{}", jsonStr);
        InformationDTO informationDTO = getFormJSON(jsonStr, InformationDTO.class);
        if(StringUtils.isBlank(informationDTO.getId()) || StringUtils
            .isBlank(informationDTO.getInformationPublicStatus()))
        {
            logger.error("Invalid parameter: id or public status canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        informationService.changePublicStatus(informationDTO, modify);
        return success(UPDATE_SUCCESS);
    }

    /**
     * 根据ID查询，详情
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/findById", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String findById(@RequestBody String jsonStr)
    {
        logger.debug("Find information by id：{}", jsonStr);

        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        InformationVO result = informationService.findById(baseId.getId());
        return success(result);
    }

    /**
     * 根据ID查询，详情
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String detail(@RequestBody String jsonStr)
    {
        logger.debug("Find information detail：{}", jsonStr);

        BaseId baseId = getFormJSON(jsonStr, BaseId.class);
        String modify = getUserNo(getPageJSON(jsonStr, Object.class));
        if(StringUtils.isBlank(baseId.getId()))
        {
            logger.error("Invalid parameter: id canot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        InformationVO result = informationService.detail(baseId.getId(), modify);
        return success(result);
    }

    /**
     * 分页查询（客户端）
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String page(@RequestBody String jsonStr)
    {
        logger.debug("Find information page：{}", jsonStr);
        PageInformationDTO informationDTO = getFormJSON(jsonStr, PageInformationDTO.class);
        if(StringUtils.isBlank(informationDTO.getUserNo()))
        {
            informationDTO.setUserNo(getUserNo(getPageJSON(jsonStr, Object.class)));
        }
        PageRequest pageRequest = genPageRequest(informationDTO);
        informationDTO.setInformationPublicStatus(DEFAULT_STATUS);

        PageDTO<InformationVO> result = informationService.page(informationDTO, pageRequest);
        return success(result);
    }

    /**
     * 分页查询（管理端）
     * @param jsonStr 用户所属组织
     * @return
     */
    @RequestMapping(value = "query", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String query(@RequestBody String jsonStr)
    {
        logger.debug("Find information query：{}", jsonStr);
        PageInformationDTO informationDTO = getFormJSON(jsonStr, PageInformationDTO.class);
        PageRequest pageRequest = genPageRequest(informationDTO);

        PageDTO<InformationVO> result = informationService.page(informationDTO, pageRequest);
        return success(result);
    }

    /**
     * 远程调用
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/rpcDetail", method = RequestMethod.POST)
    public String rpcDetail(@RequestBody String jsonStr)
    {
        CmsRichTextDTO dto = getFormJSON(jsonStr, CmsRichTextDTO.class);
        InformationVO result = informationService.detail(dto.getId(), "");
        result.setInformationContent(null);
        return success(result);
    }


    /**
     * 中农日报资讯查询
     * @return 资讯列表
     */
    @RequestMapping(value = "/indexList", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String indexList(@RequestBody String jsonStr) {
        SaveInformationIndexDTO dto = getFormJSON(jsonStr, SaveInformationIndexDTO.class);
        if(StringUtils.isBlank(dto.getArticleType())){
            logger.error("Invalid parameter: articleType cannot be null!");
            throw new InformationException(FIELD_ILLEGAL);
        }
        List<InformationVO> result = informationService.indexList(dto.getArticleType());
        return success(result);
    }

    // ------------------------------ 私有变量及方法 ---------------------------------/

    private static Logger logger = LoggerFactory.getLogger(InformationController.class);

    @Value("${default.information.node}")
    private String defaultNode;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private InformationService informationService;

}