eureka.instance.hostname=**************:1111
eureka.client.serviceUrl.defaultZone=http://**************:1111/eureka/
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}

server.port=23212

logging.level=INFO
logging.level.org.hibernate=ERROR
logging.level.org.mybatis=ERROR
logging.level.org.apache.ibatis=ERROR
logging.level.com.apec.order.mapper=info
logging.level.com.apec.framework=info
logging.level.com.apec=debug

spring.datasource.primary.url=****************************************************************************************
spring.datasource.primary.username=znw
spring.datasource.primary.password=znw@ap-ec
spring.datasource.primary.initialSize=1
spring.datasource.primary.maxActive=30
spring.datasource.primary.maxAge=120000
spring.datasource.primary.validationQuery=select 6

redis.database=1
redis.host=**************
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

spring.redis.host=**************
spring.redis.port=7000
spring.redis.host2=**************
spring.redis.port2=7001
spring.redis.timeout=5000
spring.redis.maxIdle=8
spring.redis.minIdle=4
spring.redis.maxRedirections=3000


spring.rabbitmq.host=**************
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

spring.wx.appId=wx32773492d66dfb87
spring.wx.secret=63bab0eed220ff456e612abc51092134
spring.wx.openIds=okpYuwfcGOrYN5md3O2BzJFNO1d0,okpYuwZVVmg6-whbZ4G_AWsAfUUo,okpYuwdIfCHFY502skrm6hxZH-r4,okpYuwSoDB8gwvZUJL81J9LaLzKo,okpYuwQ_qh5bsj9UFVlDH5ienAms,okpYuwZSJX8ARIUkbJZM1Bkf9Ibo
