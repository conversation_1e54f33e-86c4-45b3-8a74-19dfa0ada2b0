logging.level.com.apec=DEBUG
workerId=1

eureka.client.serviceUrl.defaultZone=http://eureka.aliyun.magpie.com:1111/eureka/

spring.datasource.primary.url=***********************************************************************************************************************************************************************
spring.datasource.primary.username=znw
spring.datasource.primary.password=admin@123

# mongodb setting
spring.data.mongodb.uri=mongodb://mongo.aliyun.magpie.com:27017/information

redis.database=0
redis.host=redis.aliyun.magpie.com
redis.port=6379
redis.password=foobared
redis.maxActive=8
redis.maxWait=-1
redis.maxIdle=8
redis.minIdle=0
redis.timeout=0

ftp.host=ftp.aliyun.magpie.com
ftp.port=21
ftp.username=root
ftp.password=znw@ap-ec
ftp.workingDir=/data/file/

spring.redis.host=redis.aliyun.magpie.com
spring.redis.port=7000
spring.redis.host2=redis.aliyun.magpie.com
spring.redis.port2=7001
spring.redis.timeout=5000
spring.redis.maxRedirections=5

spring.rabbitmq.host=rabbitmq.aliyun.magpie.com
spring.rabbitmq.port=5672
spring.rabbitmq.username=apec
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/apec

category.detail=http://information-category-service/category/queryByCategoryNo
third-party-service.uploadImage=http://THIRD-PARTY-SERVICE/ali/oss/upload