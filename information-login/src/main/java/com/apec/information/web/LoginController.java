package com.apec.information.web;

import com.apec.framework.base.BaseController;
import com.apec.framework.common.model.PageJSON;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.constant.InformationConstants;
import com.apec.information.dto.AccountDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.vo.AccountVO;
import com.apec.information.service.LoginService;
import com.apec.information.vo.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apec.framework.common.constant.FrameConsts.SESSION_ID;
import static com.apec.information.constant.InformationConstants.SUCCESS_BOOL;
import static com.apec.information.constant.InformationConstants.SUCCESS_STATUS;

/**
 * <AUTHOR>
 * @time 2017-09-16 14:04:57
 * @DESC: CmsRichTextController
 */

@RestController
@RefreshScope
public class LoginController extends BaseController
{
    private static Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private LoginService loginService;

    /**
     * 登录
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/login", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String login(@RequestBody String jsonStr) throws Exception
    {

        logger.debug("登录：{}", jsonStr);
        AccountDTO dto = getFormJSON(jsonStr, AccountDTO.class);
        if(StringUtils.isBlank(dto.getUsername()) || StringUtils.isBlank(dto.getPassword()))
        {
            logger.error("账号密码有一项为空");
            throw new InformationException(InformationConstants.LOGIN_NECESSARY_ERROR);
        }
        PageJSON<String> pageJSON = getPageJSON(jsonStr, String.class);
        String sessionId = (String)pageJSON.getRequestAttrMap().get(SESSION_ID);
        AccountVO result = loginService.login(dto, sessionId);
        return getResultJSONStr(true, result, "");
    }

    /**
     * 登录
     * @param jsonStr
     * @return
     */
    @RequestMapping(value = "/findByDingId", method = RequestMethod.POST, produces = "application/json; charset=UTF-8")
    public String findByDingId(@RequestBody String jsonStr) throws Exception
    {

        logger.debug("通过dingId查询：{}", jsonStr);
        AccountDTO dto = JsonUtils.parseObject(jsonStr, AccountDTO.class);
        if(StringUtils.isBlank(dto.getUserDingId()))
        {
            logger.error("id未查询");
            throw new InformationException(InformationConstants.LOGIN_NECESSARY_ERROR);
        }
        UserVO result = loginService.findByDingId(dto.getUserDingId());
        return getResultJSONStr(SUCCESS_BOOL, result, SUCCESS_STATUS);
    }
}