package com.apec.information.model;

import com.apec.framework.jpa.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static com.apec.framework.common.constant.FrameConsts.ASSIGNED;
import static com.apec.framework.common.constant.FrameConsts.SYSTEM_GENERATOR;

/**
 * @date 2018-09-17
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "information_account")
@GenericGenerator(name = SYSTEM_GENERATOR, strategy = ASSIGNED)
public class InformationAccountModel extends BaseModel<String>
{
    /**
     * 组织编号
     */
    @Column(name = "ORG_NO")
    private String orgNo;

    /**
     * 组织编号
     */
    @Column(name = "ORG_NAME")
    private String orgName;

    /**
     * 钉钉id
     */
    @Column(name = "USER_DING_ID")
    private String userDingId;

    /**
     * 用户名
     */
    @Column(name = "USER_NAME")
    private String userName;

    /**
     * 角色编号
     */
    @Column(name = "ROLE_NO")
    private String roleNo;

    /**
     * 角色名
     */
    @Column(name = "ROLE_NAME")
    private String roleName;

    /**
     * 账号
     */
    @Column(name = "ACCOUNT_NAME")
    private String accountName;

    /**
     * 密码
     */
    @Column(name = "ACCOUNT_PASSWORD")
    private String accountPassword;
}