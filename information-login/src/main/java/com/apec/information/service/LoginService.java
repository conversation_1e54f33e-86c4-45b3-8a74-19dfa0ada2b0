package com.apec.information.service;

import com.apec.information.dto.AccountDTO;
import com.apec.information.vo.AccountVO;
import com.apec.information.vo.UserVO;

/**
 * <AUTHOR>
 * @date 2018-09-17
 */
public interface LoginService
{
    /**
     * 登录
     * @param dto
     * @param sessionId
     * @return
     */
    AccountVO login(AccountDTO dto, String sessionId) throws Exception;

    /**
     * 通过钉钉id查询用户
     * @param userDingId
     * @return
     */
    UserVO findByDingId(String userDingId);
}