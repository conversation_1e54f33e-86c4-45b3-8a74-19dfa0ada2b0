package com.apec.information.service.impl;

import com.apec.framework.cache.CacheService;
import com.apec.framework.common.constant.FrameConsts;
import com.apec.framework.common.enumtype.EnableFlag;
import com.apec.framework.common.util.BeanUtils;
import com.apec.framework.common.util.JsonUtils;
import com.apec.information.dao.AccountDAO;
import com.apec.information.dto.AccountDTO;
import com.apec.information.handler.InformationException;
import com.apec.information.model.InformationAccountModel;
import com.apec.information.model.QInformationAccountModel;
import com.apec.information.service.LoginService;
import com.apec.information.vo.AccountVO;
import com.apec.information.vo.UserVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.apec.framework.common.constant.FrameConsts.PREFIX_SESSIONID;
import static com.apec.information.constant.InformationConstants.*;

/**
 * 员工账户服务的实现
 * @date 2017-07-04 7:49
 * <AUTHOR>
 */
@Service
public class LoginServiceImpl implements LoginService
{
    @Override
    public AccountVO login(AccountDTO accountDTO, String sessionId) throws Exception
    {
        BooleanExpression predicate = qAccount.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qAccount.accountName.eq(accountDTO.getUsername()));
        predicate = predicate.and(qAccount.accountPassword.eq(accountDTO.getPassword()));

        InformationAccountModel account = accountDAO.findOne(predicate);
        if(null == account || EnableFlag.Y != account.getEnableFlag())
        {
            throw new InformationException(LOGIN_ERROR);
        }
        saveCache(account, sessionId);
        return toResult(account);
    }

    @Override
    public UserVO findByDingId(String userDingId)
    {
        String key = CACHE_USER_DETAIL + userDingId;
        String value = cacheService.get(key);
        if(StringUtils.isNotBlank(value))
        {
            return JsonUtils.parseObject(value, UserVO.class);
        }
        BooleanExpression predicate = qAccount.enableFlag.eq(EnableFlag.Y);
        predicate = predicate.and(qAccount.userDingId.eq(userDingId));
        InformationAccountModel account = accountDAO.findOne(predicate);
        UserVO result = new UserVO();
        BeanUtils.copyPropertiesIgnoreNullFilds(account, result);
        cacheService.add(key, JsonUtils.toJSONString(result), sessionTimeOut);
        return result;
    }

    /********************************* 私有 **********************************/

    private QInformationAccountModel qAccount = QInformationAccountModel.informationAccountModel;

    @Value("${session.time.out}")
    private Integer sessionTimeOut;

    @Autowired
    private AccountDAO accountDAO;

    @Autowired
    private CacheService cacheService;

    private AccountVO toResult(InformationAccountModel account)
    {
        AccountVO result = new AccountVO();
        result.setUserId(account.getId());
        result.setUserNo(account.getUserDingId());
        result.setRealName(account.getUserName());
        result.setOrgNo(account.getOrgNo());
        result.setOrgName(account.getOrgName());
        return result;
    }

    private void saveCache(InformationAccountModel account, String sessionId)
    {
        String sessionKey = PREFIX_SESSIONID + sessionId;
        String marketerKey = CACHE_USER + account.getUserDingId();
        String lastSession = cacheService.get(marketerKey);
        if(StringUtils.isNotBlank(lastSession))
        {
            // 移除旧有session缓存
            cacheService.remove(PREFIX_SESSIONID + lastSession);
        }
        // 增加session缓存
        cacheService.add(sessionKey, account.getUserDingId(), sessionTimeOut);
    }
}