package com.apec;

import com.apec.framework.base.BaseApplication;
import com.apec.framework.common.tools.MD5;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @date 2018-09-17
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class LoginApplication extends BaseApplication
{
    public static void main(String[] args)
    {
        new SpringApplicationBuilder(LoginApplication.class).web(true).run(args);
    }
}